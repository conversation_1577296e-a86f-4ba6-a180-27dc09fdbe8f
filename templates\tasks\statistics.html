{% extends 'base.html' %}

{% block title %}任务统计 - DINet 口型同步系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-2"></i>任务统计</h2>
    <a href="{% url 'task_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-2"></i>返回任务列表
    </a>
</div>

<!-- 总体统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_stats.total }}</h4>
                        <p class="mb-0">总任务数</p>
                    </div>
                    <i class="fas fa-tasks fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_stats.waiting }}</h4>
                        <p class="mb-0">等待中</p>
                    </div>
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_stats.inference }}</h4>
                        <p class="mb-0">处理中</p>
                    </div>
                    <i class="fas fa-cog fa-spin fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_stats.finished }}</h4>
                        <p class="mb-0">已完成</p>
                    </div>
                    <i class="fas fa-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 每日任务趋势 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>最近7天任务创建趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="dailyChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 状态分布 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>任务状态分布</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 用户统计 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users me-2"></i>用户任务统计 (Top 10)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>用户名</th>
                                <th>总任务数</th>
                                <th>已完成</th>
                                <th>完成率</th>
                                <th>进度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in user_stats %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <i class="fas fa-user me-1"></i>
                                    {{ user.username|default:"未知用户" }}
                                </td>
                                <td>{{ user.task_count }}</td>
                                <td>{{ user.finished_count }}</td>
                                <td>
                                    {% widthratio user.finished_count user.task_count 100 %}%
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" 
                                             style="width: {% widthratio user.finished_count user.task_count 100 %}%">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">暂无数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 每日任务趋势图
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: [{% for day in daily_stats %}'{{ day.date }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: '任务创建数',
            data: [{% for day in daily_stats %}{{ day.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// 状态分布饼图
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['等待中', '处理中', '已完成', '失败'],
        datasets: [{
            data: [
                {{ total_stats.waiting }},
                {{ total_stats.inference }},
                {{ total_stats.finished }},
                {{ total_stats.failed }}
            ],
            backgroundColor: [
                '#ffc107',
                '#17a2b8', 
                '#28a745',
                '#dc3545'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}