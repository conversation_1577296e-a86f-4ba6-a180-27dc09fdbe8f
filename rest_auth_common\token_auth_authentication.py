# -*- coding: utf-8 -*-
from __future__ import absolute_import
from __future__ import unicode_literals
from django.conf import settings

from rest_framework import authentication
from rest_framework import exceptions

from .models import AutoExpiredAuthToken

CHECK_EXPIRE = getattr(settings, 'REST_AUTH_COMMON', {}).get('check_expire', True)


class TokenAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        auth = authentication.get_authorization_header(request).split()
        if auth and auth[0] == b'token':
            if len(auth) != 3:
                raise exceptions.AuthenticationFailed('Token auth should be token $user_id $key')
            user_id = auth[1].decode("ascii")
            key = auth[2].decode("ascii")
        elif 'user_id' in request.query_params and request.query_params['user_id'] and 'key' in request.query_params:
            user_id = request.query_params['user_id']
            key = request.query_params['key']
        elif request.data is not None and 'user_id' in request.data and 'key' in request.data:
            user_id = request.data['user_id']
            key = request.data['key']
        else:
            return
        if not user_id:
            raise exceptions.AuthenticationFailed('user_id is empty')
        try:
            token = AutoExpiredAuthToken.objects.get(user_id=user_id)
        except AutoExpiredAuthToken.DoesNotExist:
            raise exceptions.AuthenticationFailed("Can't get user token object %s" % user_id)
        if token.key != key:
            raise exceptions.AuthenticationFailed('Invalid key for user %s' % token.user_id)
        if token.expire_in < 0 and CHECK_EXPIRE:
            raise exceptions.AuthenticationFailed('Key expired for user %s' % token.user_id)
        return token.user, None
