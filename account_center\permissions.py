from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser, AllowAny

CUSTOM_PERMISSIONS = (
    (
        'account_center.account',
        {
            'GET': IsAuthenticated,
            'POST': IsAdminUser,
            'PUT': IsAuthenticated,
            'DELETE': IsAdminUser,
            # 'info': IsAuthenticated,
            'change_self_rsa_password': {'POST': IsAuthenticated},
            'change_account_rsa_password': {'POST': IsAdminUser},
        }
    ),
    (
        'account_center.org',
        {
            'GET': IsAuthenticated,
            'POST': IsAdminUser,
            'PUT': IsAdminUser,
            'DELETE': IsAdminUser,
        }
    ),
)
