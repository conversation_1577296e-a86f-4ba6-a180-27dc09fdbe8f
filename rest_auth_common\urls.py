# -*- coding: utf-8 -*-
from django.conf.urls import url
from django.conf import settings
from . import views as _views


urlpatterns = [
    url(r'^rsa/((?P<action>(get_public_key|create_key_pair))/)?$', _views.RsaKeyPairView.as_view(), name='rest_auth_common.rsa'),
    url(r'^captcha/((?P<action>())/)?$', _views.CaptchaView.as_view(), name='rest_auth_common.captcha'),
]

auth_url = url(r'^auth/((?P<action>(rsa_login|password_login|change_password|create_enterprise_app_id|eaccess_token_login))/)?$', _views.Auth.as_view(), name='rest_auth_common.auth')
if hasattr(settings, "ONLY_RSA_LOGIN"):
    ONLY_RSA_LOGIN = getattr(settings, "ONLY_RSA_LOGIN")
    if ONLY_RSA_LOGIN:
        auth_url = url(r'^auth/((?P<action>(rsa_login|change_password|create_enterprise_app_id|eaccess_token_login))/)?$', _views.Auth.as_view(), name='rest_auth_common.auth')
urlpatterns.append(auth_url)

