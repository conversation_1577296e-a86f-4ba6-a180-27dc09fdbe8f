# coding=utf-8

import copy

from django.db import models
from django_filters import filters, FilterSet
from django_filters.filterset import STRICTNESS, FILTER_FOR_DBFIELD_DEFAULTS
from django import forms


class NumberInFilter(filters.BaseInFilter, filters.NumberFilter):
    pass


class CustomNullBooleanSelect(forms.NullBooleanSelect):
    def value_from_datadict(self, data, files, name):
        value = data.get(name, None)
        return {'2': True,
                True: True,
                'True': True,
                '3': False,
                'False': False,
                False: False,
                'true': True,
                'false': False}.get(value, None)


class CustomNullBooleanField(forms.NullBooleanField):
    widget = CustomNullBooleanSelect


class CustomBooleanFilter(filters.Filter):
    field_class = CustomNullBooleanField


FILTER_DEFAULT_MAP = copy.deepcopy(FILTER_FOR_DBFIELD_DEFAULTS)
FILTER_DEFAULT_MAP[models.BooleanField] = {'filter_class': CustomBooleanFilter}


class CustomFilter(FilterSet):
    # 否则之前的接口会被强制根据order_by的第一个排序
    strict = STRICTNESS.RAISE_VALIDATION_ERROR
    FILTER_DEFAULTS = FILTER_DEFAULT_MAP
