import os
import sys
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from omegaconf import OmegaConf

from form_api.tools import (
    inference, 
    clear_inference_cache, 
    get_inference_cache_stats,
    check_dependencies
)


class Command(BaseCommand):
    help = 'DINet推理管理命令 - 支持视频音频同步、缓存管理等功能'

    def add_arguments(self, parser):
        # 子命令
        subparsers = parser.add_subparsers(dest='action', help='可用的操作')
        
        # 推理命令
        inference_parser = subparsers.add_parser('run', help='运行DINet推理')
        inference_parser.add_argument(
            '--video', 
            required=True,
            help='输入视频文件路径'
        )
        inference_parser.add_argument(
            '--audio', 
            required=True,
            help='输入音频文件路径'
        )
        inference_parser.add_argument(
            '--output', 
            required=True,
            help='输出视频文件路径'
        )
        inference_parser.add_argument(
            '--config',
            default='form_api/configs/dinet_config.yaml',
            help='配置文件路径 (默认: form_api/configs/dinet_config.yaml)'
        )
        inference_parser.add_argument(
            '--checkpoint',
            default='form_api/checkpoints/dinet_model.pth',
            help='模型检查点路径 (默认: form_api/checkpoints/dinet_model.pth)'
        )
        inference_parser.add_argument(
            '--seed',
            type=int,
            default=1247,
            help='随机种子 (默认: 1247, -1表示随机)'
        )
        inference_parser.add_argument(
            '--no-cache',
            action='store_true',
            help='禁用缓存功能'
        )
        
        # 缓存管理命令
        cache_parser = subparsers.add_parser('cache', help='缓存管理')
        cache_subparsers = cache_parser.add_subparsers(dest='cache_action', help='缓存操作')
        
        cache_subparsers.add_parser('clear', help='清空缓存')
        cache_subparsers.add_parser('stats', help='显示缓存统计')
        
        # 系统检查命令
        subparsers.add_parser('check', help='检查系统依赖和配置')

    def handle(self, *args, **options):
        action = options.get('action')
        
        if not action:
            self.print_help('manage.py', 'dinet_inference')
            return
            
        try:
            if action == 'run':
                self.handle_inference(options)
            elif action == 'cache':
                self.handle_cache(options)
            elif action == 'check':
                self.handle_check()
            else:
                raise CommandError(f'未知操作: {action}')
                
        except Exception as e:
            raise CommandError(f'命令执行失败: {str(e)}')

    def handle_inference(self, options):
        """处理推理命令"""
        self.stdout.write(self.style.SUCCESS('🚀 开始DINet推理...'))
        
        # 验证输入文件
        video_path = options['video']
        audio_path = options['audio']
        output_path = options['output']
        
        if not os.path.exists(video_path):
            raise CommandError(f'视频文件不存在: {video_path}')
            
        if not os.path.exists(audio_path):
            raise CommandError(f'音频文件不存在: {audio_path}')
            
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            self.stdout.write(f'📁 创建输出目录: {output_dir}')
        
        # 加载配置
        config_path = options['config']
        if not os.path.exists(config_path):
            raise CommandError(f'配置文件不存在: {config_path}')
            
        try:
            config = OmegaConf.load(config_path)
            self.stdout.write(f'📋 加载配置文件: {config_path}')
        except Exception as e:
            raise CommandError(f'配置文件加载失败: {e}')
        
        # 检查模型文件
        checkpoint_path = options['checkpoint']
        if not os.path.exists(checkpoint_path):
            raise CommandError(f'模型检查点不存在: {checkpoint_path}')
        
        # 显示参数信息
        self.stdout.write('📊 推理参数:')
        self.stdout.write(f'   视频文件: {video_path}')
        self.stdout.write(f'   音频文件: {audio_path}')
        self.stdout.write(f'   输出文件: {output_path}')
        self.stdout.write(f'   模型文件: {checkpoint_path}')
        self.stdout.write(f'   随机种子: {options["seed"]}')
        self.stdout.write(f'   缓存状态: {"禁用" if options["no_cache"] else "启用"}')
        
        # 执行推理
        try:
            result_path = inference(
                config=config,
                video_path=video_path,
                audio_path=audio_path,
                video_out_path=output_path,
                inference_ckpt_path=checkpoint_path,
                seed=options['seed'],
                enable_cache=not options['no_cache']
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✅ 推理完成! 输出文件: {result_path}')
            )
            
        except Exception as e:
            raise CommandError(f'推理执行失败: {str(e)}')

    def handle_cache(self, options):
        """处理缓存管理命令"""
        cache_action = options.get('cache_action')
        
        if cache_action == 'clear':
            self.stdout.write('🗑️  清空缓存...')
            success = clear_inference_cache()
            if success:
                self.stdout.write(self.style.SUCCESS('✅ 缓存清空完成'))
            else:
                self.stdout.write(self.style.ERROR('❌ 缓存清空失败'))
                
        elif cache_action == 'stats':
            self.stdout.write('📊 获取缓存统计...')
            stats = get_inference_cache_stats()
            if stats:
                self.stdout.write(self.style.SUCCESS('📈 缓存统计信息:'))
                for key, value in stats.items():
                    self.stdout.write(f'   {key}: {value}')
            else:
                self.stdout.write(self.style.WARNING('⚠️  无法获取缓存统计信息'))
                
        else:
            self.stdout.write(self.style.ERROR('❌ 请指定缓存操作: clear 或 stats'))

    def handle_check(self):
        """处理系统检查命令"""
        self.stdout.write('🔍 检查系统依赖...')
        
        # 检查依赖
        deps_ok, missing_deps = check_dependencies()
        
        if deps_ok:
            self.stdout.write(self.style.SUCCESS('✅ 所有依赖检查通过'))
        else:
            self.stdout.write(self.style.ERROR('❌ 缺少以下依赖:'))
            for dep in missing_deps:
                self.stdout.write(f'   - {dep}')
        
        # 检查配置文件
        config_files = [
            'form_api/configs/dinet_config.yaml',
            'form_api/checkpoints/dinet_model.pth',
        ]
        
        self.stdout.write('\n📋 检查配置文件:')
        for config_file in config_files:
            if os.path.exists(config_file):
                self.stdout.write(self.style.SUCCESS(f'   ✅ {config_file}'))
            else:
                self.stdout.write(self.style.ERROR(f'   ❌ {config_file} (不存在)'))
        
        # 检查目录权限
        self.stdout.write('\n📁 检查目录权限:')
        test_dirs = ['./dinet_cache', './media', './logs']
        for test_dir in test_dirs:
            try:
                os.makedirs(test_dir, exist_ok=True)
                # 测试写入权限
                test_file = os.path.join(test_dir, '.test_write')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                self.stdout.write(self.style.SUCCESS(f'   ✅ {test_dir} (可读写)'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'   ❌ {test_dir} (权限错误: {e})'))