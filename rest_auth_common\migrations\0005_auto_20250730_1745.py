# Generated by Django 3.2 on 2025-07-30 09:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rest_auth_common', '0004_rsakeypair'),
    ]

    operations = [
        migrations.AlterField(
            model_name='appidsecret',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='autoexpiredauthtoken',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='enterpriseappidsecret',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='rsakeypair',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='userenterpriseappidsecret',
            name='id',
            field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
    ]
