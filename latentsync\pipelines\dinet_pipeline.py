# Adapted from https://github.com/guoyww/AnimateDiff/blob/main/animatediff/pipelines/pipeline_animation.py

import inspect
import os
import shutil
from typing import Callable, List, Optional, Union
import subprocess

import numpy as np
import torch
import torchvision

# from diffusers.utils import is_accelerate_available
# from packaging import version

# from diffusers.configuration_utils import FrozenDict
# from diffusers.models import AutoencoderKL
# from diffusers.pipelines.pipeline_utils import DiffusionPipeline

from diffusers.utils import deprecate, logging

from einops import rearrange

# from ..models.DINet_old import DINet
from ..utils.image_processor import ImageProcessor
from ..utils.util import read_video, read_audio, write_video
from ..whisper.audio2feature import Audio2Feature
from tqdm import tqdm

import soundfile as sf

logger = logging.get_logger(__name__)  # pylint: disable=invalid-name


class DINetPipeline:

    def __init__(
        self,
        dinet,
        audio_encoder,
        device='cuda:0',
        weight_dtype=torch.float32
    ):
        self.dinet = dinet.to(device)
        self.audio_encoder = audio_encoder
        self.device = device
        self.weight_dtype = weight_dtype


    @staticmethod
    def paste_surrounding_pixels_back(decoded_latents, pixel_values, masks, device, weight_dtype):
        # Paste the surrounding pixels back, because we only want to change the mouth region
        pixel_values = pixel_values.to(device=device, dtype=weight_dtype)
        masks = masks.to(device=device, dtype=weight_dtype)
        combined_pixel_values = decoded_latents * masks + pixel_values * (1 - masks)
        return combined_pixel_values

    @staticmethod
    def pixel_values_to_images(pixel_values: torch.Tensor):
        pixel_values = rearrange(pixel_values, "f c h w -> f h w c")
        pixel_values = (pixel_values / 2 + 0.5).clamp(0, 1)
        images = (pixel_values * 255).to(torch.uint8)
        images = images.cpu().numpy()
        return images

    def affine_transform_video(self, video_path):
        video_frames = read_video(video_path, use_decord=True)
        faces = []
        boxes = []
        affine_matrices = []
        point_list = []
        print(f"Affine transforming {len(video_frames)} faces...")
        for frame in tqdm(video_frames):
            face, box, affine_matrix,points = self.image_processor.affine_transform(frame)
            faces.append(face)
            boxes.append(box)
            affine_matrices.append(affine_matrix)
            point_list.append(points)

        faces = torch.stack(faces)
        return faces, video_frames, boxes, affine_matrices,point_list

    def restore_video(self, faces, video_frames, boxes, affine_matrices):
        video_frames = video_frames[: faces.shape[0]]
        out_frames = []
        for index, face in enumerate(faces):
            x1, y1, x2, y2 = boxes[index]
            height = int(y2 - y1)
            width = int(x2 - x1)
            face = torchvision.transforms.functional.resize(face, size=(height, width), antialias=True)
            # face = rearrange(face, "c h w -> h w c")
            # face = (face / 2 + 0.5).clamp(0, 1)
            # face = (face * 255).to(torch.uint8).cpu().numpy()
            out_frame = self.image_processor.restorer.restore_img(video_frames[index], face, affine_matrices[index])
            out_frames.append(out_frame)
        return np.stack(out_frames, axis=0)

    @torch.no_grad()
    def match_input_to_target(self,l,l_ref):
        '''
        get the target indices here according to the different video length
        '''
        res_indx = []
        if l <= l_ref:
            # res_indx = list(range(l))
            res_indx = list(range(l_ref))
        else:
            cnt = 0
            seq = list(range(l_ref))
            seq_reverse = list(reversed(seq))
            while len(res_indx) < l:
                if cnt % 2 == 0:
                    res_indx.extend(seq)
                else:
                    res_indx.extend(seq_reverse)
                cnt += 1
            res_indx = res_indx[:l]
        return res_indx

    def obtain_mouth_openness(self,points_list,nchosen=5):
        '''
        get the mouth openness here according to the mouth keypoints
        '''
        mouth_openness = []
        # mouth_indices = [51,57,62,66]
        for points in points_list:
            openness = np.linalg.norm(points[62,:]-points[60,:]) / (np.linalg.norm(points[52,:] - points[61,:]) + 1e-6)
            mouth_openness.append(openness)
        # select the frame index according to the mouth openness
        mouth_openness = np.array(mouth_openness)
        ref_idx = np.argsort(mouth_openness)
        idx = np.linspace(0,len(ref_idx)-1,num=nchosen,dtype=int)
        ref_idx = ref_idx[idx]
        return ref_idx

    @torch.no_grad()
    def __call__(
        self,
        video_path: str,
        audio_path: str,
        video_out_path: str,
        num_frames: int = 16,
        video_fps: int = 25,
        audio_sample_rate: int = 16000,
        height: Optional[int] = None,
        width: Optional[int] = None,
        mask: str = "fix_mask",
        **kwargs,
    ):

        is_train = self.dinet.training
        self.dinet.eval()

        # 0. Define call parameters
        batch_size = 1
        num_ref_frames = self.dinet.ref_channel // self.dinet.source_channel
        assert num_ref_frames == 5, "Currently only support 5 reference frames."

        device = self.device
        self.image_processor = ImageProcessor(height, device='cuda')
        # self.image_processor_for_output = ImageProcessor(height,mask='face',device='cuda')


        video_frames, original_video_frames, boxes, affine_matrices,points_list = self.affine_transform_video(video_path)
        audio_samples = read_audio(audio_path)

        self.video_fps = video_fps

        if True:
            whisper_feature = self.audio_encoder.audio2feat(audio_path)
            whisper_chunks = self.audio_encoder.feature2chunks(feature_array=whisper_feature, fps=video_fps)

            # num_inferences = min(len(video_frames), len(whisper_chunks)) // num_frames
            num_inferences = len(whisper_chunks) // num_frames
        else:
            num_inferences = len(video_frames) // num_frames

        synced_video_frames = []

        res_indx = self.match_input_to_target(len(whisper_chunks),len(video_frames))
        # for processing the long loop videos here
        original_video_frames_loop = []
        bboxes_loop = []
        affine_matrices_loop = []

        # select fixed ref frames for the video inferences here for smoothess synthesize results
        # ref_idx = np.linspace(0,len(video_frames)-1,num_ref_frames,dtype=int)
        num_ref_frames = 5
        ref_idx = self.obtain_mouth_openness(points_list,nchosen=num_ref_frames)
        ref_frames = torch.stack([video_frames[ix] for ix in ref_idx])
        ref_frames, _, _ = self.image_processor.prepare_masks_and_masked_images(
                ref_frames, affine_transform=False
            )
        ref_frames = ref_frames.to(device=device, dtype=self.weight_dtype)
        ref_frames = rearrange(ref_frames, "f c h w -> 1 (f c) h w")
        ref_frames = ref_frames.expand(num_frames, -1, -1, -1)

        for i in tqdm(range(num_inferences), desc="Doing inference..."):

            # 16 x 50 x 384
            audio_embeds = torch.stack(whisper_chunks[i * num_frames : (i + 1) * num_frames])
            audio_embeds = audio_embeds.to(device, dtype=self.weight_dtype)
            audio_embeds = rearrange(audio_embeds, "b t c -> b c t")

            # inference_video_frames = video_frames[i * num_frames : (i + 1) * num_frames]
            inference_video_frames = video_frames[res_indx[i * num_frames : (i + 1) * num_frames]]

            warped_landmark106 = [points_list[idx] for idx in res_indx[i * num_frames : (i + 1) * num_frames]]

            original_video_frames_loop.append(original_video_frames[res_indx[i * num_frames : (i + 1) * num_frames]])
            bboxes_loop.extend([boxes[ix] for ix in res_indx[i * num_frames : (i + 1) * num_frames]])
            affine_matrices_loop.extend([affine_matrices[ix] for ix in res_indx[i * num_frames : (i + 1) * num_frames]])

            pixel_values, masked_pixel_values, masks = self.image_processor.prepare_masks_and_masked_images(
                inference_video_frames, affine_transform=False
            )


            # img = 255 * (pixel_values[0] + 1) / 2
            # img = img.permute(1,2,0).cpu().numpy().astype(np.uint8)
            # import cv2
            # cv2.imwrite("img.png",img)
            # assert 0
            # _, _, blend_masks = self.image_processor_for_output.prepare_masks_and_masked_images(
            #     inference_video_frames, affine_transform=False
            # )
            blend_masks = self.image_processor.get_mouth_masks(warped_landmark106)

            masked_pixel_values = masked_pixel_values.to(device=device,dtype=self.weight_dtype)
            decoded_latents = self.dinet(masked_pixel_values, ref_frames, audio_embeds)

            # decoded_latents = self.paste_surrounding_pixels_back(
            #     decoded_latents, pixel_values, 1 - masks, device, self.weight_dtype
            # )

            decoded_latents = self.paste_surrounding_pixels_back(
                decoded_latents, pixel_values, blend_masks, device, self.weight_dtype
            )

            synced_video_frames.append(decoded_latents)

        temp_dir = "temp"
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir, exist_ok=True)

        # write_video(os.path.join(temp_dir, "video.mp4"), torch.cat(synced_video_frames).cpu().numpy(), fps=25)
        # assert 0

        synced_video_frames = self.restore_video(
            torch.cat(synced_video_frames), np.concatenate(original_video_frames_loop,axis=0), bboxes_loop, affine_matrices_loop
        )
        # masked_video_frames = self.restore_video(
        #     torch.cat(masked_video_frames), original_video_frames, boxes, affine_matrices
        # )

        audio_samples_remain_length = int(synced_video_frames.shape[0] / video_fps * audio_sample_rate)
        audio_samples = audio_samples[:audio_samples_remain_length].cpu().numpy()

        if is_train:
            self.dinet.train()


        write_video(os.path.join(temp_dir, "video.mp4"), synced_video_frames, fps=25)

        # write_video(os.path.join(temp_dir, "video_mask.mp4"), masked_video_frames, fps=25)
        # assert 0

        sf.write(os.path.join(temp_dir, "audio.wav"), audio_samples, audio_sample_rate)

        # command = f"ffmpeg -y -loglevel error -nostdin -i {os.path.join(temp_dir, 'video.mp4')} -i {os.path.join(temp_dir, 'audio.wav')} -c:v libx264 -c:a aac -q:v 0 -q:a 0 {video_out_path} && rm -rf {temp_dir}"
        command = f"ffmpeg -y -loglevel error -nostdin -i {os.path.join(temp_dir, 'video.mp4')} -i {os.path.join(temp_dir, 'audio.wav')} -c:v libx264 -c:a aac -q:v 0 -q:a 0 {video_out_path}"
        subprocess.run(command, shell=True)

        if os.path.isdir(temp_dir):
            shutil.rmtree(temp_dir)

        # clear the memory to avoid the leakage here
        import gc
        gc.collect()
        torch.cuda.empty_cache()
