version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - MYSQL_DATABASE=dinet_db
      - MYSQL_USER=dinet_user
      - MYSQL_PASSWORD=dinet_password
      - DB_HOST=db
      - REDIS_HOST=redis
      - REDIS_PASSWORD=
    volumes:
      - ./logs:/opt/logs/tangyinx
      - ./media:/app/media
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=dinet_db
      - MYSQL_USER=dinet_user
      - MYSQL_PASSWORD=dinet_password
      - MYSQL_ROOT_PASSWORD=root_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data: