import json


class ParameterTypeError(Exception):
    pass


class ParameterError(Exception):
    pass


class ParameterForSerializerError(Exception):
    def __init__(self, error_data):
        self.error_data = error_data

    @property
    def message(self):
        return json.dumps(self.error_data)

    def __repr__(self):
        return self.message

    def __str__(self):
        return self.message
