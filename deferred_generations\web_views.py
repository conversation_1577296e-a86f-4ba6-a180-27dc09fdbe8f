"""
Web 页面视图模块

提供前端页面的视图处理，包括：
- 用户登录页面
- 任务管理页面
- 任务统计和分页
"""

import logging
from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin

from .models import DeferredGenerationTask

logger = logging.getLogger(__name__)

# 状态选择常量
STATUS_CHOICES = [
    ('waiting', '等待中'),
    ('inference', '处理中'),
    ('finished', '已完成'),
    ('failed', '失败'),
]


def login_view(request):
    """用户登录页面"""
    if request.user.is_authenticated:
        return redirect('task_list')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        remember_me = request.POST.get('remember_me')

        if username and password:
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)

                # 设置会话过期时间
                if not remember_me:
                    request.session.set_expiry(0)  # 浏览器关闭时过期
                else:
                    request.session.set_expiry(1209600)  # 2周

                messages.success(request, f'欢迎回来，{user.username}！')

                # 重定向到原来要访问的页面或任务列表
                next_url = request.GET.get('next', 'task_list')
                return redirect(next_url)
            else:
                messages.error(request, '用户名或密码错误，请重试。')
        else:
            messages.error(request, '请输入用户名和密码。')

    return render(request, 'auth/login.html')


def logout_view(request):
    """用户登出"""
    username = request.user.username if request.user.is_authenticated else None
    logout(request)

    if username:
        messages.success(request, f'再见，{username}！您已成功登出。')

    return redirect('login')


class TaskListView(LoginRequiredMixin, ListView):
    """任务列表页面视图"""

    model = DeferredGenerationTask
    template_name = 'tasks/task_list.html'
    context_object_name = 'tasks'
    paginate_by = 12
    login_url = 'login'

    def get_queryset(self):
        """获取任务查询集"""
        queryset = DeferredGenerationTask.objects.all()

        # 状态筛选
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # 用户名搜索
        username = self.request.GET.get('username')
        if username:
            queryset = queryset.filter(
                Q(username__icontains=username) |
                Q(user_id=username if username.isdigit() else 0)
            )

        # 排序
        ordering = self.request.GET.get('o', '-created_at')
        if ordering in ['created_at', '-created_at', 'updated_at', '-updated_at', 'pk', '-pk']:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by('-created_at')

        return queryset

    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)

        # 任务统计
        stats = DeferredGenerationTask.objects.aggregate(
            waiting=Count('id', filter=Q(status='waiting')),
            inference=Count('id', filter=Q(status='inference')),
            finished=Count('id', filter=Q(status='finished')),
            failed=Count('id', filter=Q(status='failed'))
        )
        context['stats'] = stats

        # 状态选择
        context['status_choices'] = STATUS_CHOICES

        return context


@login_required
def task_detail_ajax(request, task_id):
    """AJAX 获取任务详情"""
    try:
        task = DeferredGenerationTask.objects.get(id=task_id)

        data = {
            'id': task.id,
            'user_id': task.user_id,
            'username': task.username,
            'source_video': task.source_video,
            'source_audio': task.source_audio,
            'generated_video_path': task.generated_video_path,
            'status': task.status,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat(),
        }

        return JsonResponse({
            'error_code': 0,
            'error_reason': 'ok',
            'data': data
        })

    except DeferredGenerationTask.DoesNotExist:
        return JsonResponse({
            'error_code': 404,
            'error_reason': '任务不存在',
            'data': {}
        }, status=404)

    except Exception as e:
        logger.error(f"获取任务详情失败: {e}", exc_info=True)
        return JsonResponse({
            'error_code': 500,
            'error_reason': f'服务器错误: {str(e)}',
            'data': {}
        }, status=500)


@login_required
def download_result(request, task_id):
    """下载任务结果文件"""
    try:
        task = DeferredGenerationTask.objects.get(id=task_id)

        if not task.generated_video_path:
            raise Http404("任务结果文件不存在")

        # 检查文件是否存在
        import os
        if not os.path.exists(task.generated_video_path):
            raise Http404("结果文件不存在")

        # 返回文件下载响应
        with open(task.generated_video_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='video/mp4')
            response['Content-Disposition'] = f'attachment; filename="result_{task_id}.mp4"'
            return response

    except DeferredGenerationTask.DoesNotExist:
        raise Http404("任务不存在")

    except Exception as e:
        logger.error(f"下载文件失败: {e}", exc_info=True)
        raise Http404("下载失败")


@login_required
def task_statistics(request):
    """任务统计页面"""
    # 总体统计
    total_stats = DeferredGenerationTask.objects.aggregate(
        total=Count('id'),
        waiting=Count('id', filter=Q(status='waiting')),
        inference=Count('id', filter=Q(status='inference')),
        finished=Count('id', filter=Q(status='finished')),
        failed=Count('id', filter=Q(status='failed'))
    )

    # 用户统计
    user_stats = DeferredGenerationTask.objects.values('username').annotate(
        task_count=Count('id'),
        finished_count=Count('id', filter=Q(status='finished'))
    ).order_by('-task_count')[:10]

    # 每日统计（最近7天）
    from django.utils import timezone
    from datetime import timedelta

    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=6)

    daily_stats = []
    for i in range(7):
        date = start_date + timedelta(days=i)
        count = DeferredGenerationTask.objects.filter(
            created_at__date=date
        ).count()
        daily_stats.append({
            'date': date.strftime('%m-%d'),
            'count': count
        })

    context = {
        'total_stats': total_stats,
        'user_stats': user_stats,
        'daily_stats': daily_stats,
    }

    return render(request, 'tasks/statistics.html', context)


# 错误页面处理
def handler404(request, exception):
    """404 错误页面"""
    return render(request, 'errors/404.html', status=404)


def handler500(request):
    """500 错误页面"""
    return render(request, 'errors/500.html', status=500)
