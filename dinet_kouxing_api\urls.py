from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from deferred_generations import web_views
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('watchman/', include('watchman.urls')),

    # API 路由
    path('auth/', include('rest_auth_common.urls')),
    path('account/', include('account_center.urls')),
    path('file/', include('filestorage.urls')),
    path('task/', include('deferred_generations.urls')),

    # Web 页面路由
    path('', web_views.TaskListView.as_view(), name='home'),
    path('media-center/', TemplateView.as_view(template_name='media/media_center.html'), name='media_center'),
    path('login/', web_views.login_view, name='login'),
    path('logout/', web_views.logout_view, name='logout'),
]

# 静态文件服务（开发环境）
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# 错误页面处理
handler404 = 'deferred_generations.web_views.handler404'
handler500 = 'deferred_generations.web_views.handler500'
