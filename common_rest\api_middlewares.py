# -*- coding: utf-8 -*-


from django.core.cache import cache
from rest_framework import exceptions

try:
    from threading import local
except ImportError:
    from django.utils._threading_local import local

_thread_locals = local()


class BaseApiMiddleware(object):
    def preprocess_request(self, request, view):
        pass

    def process_response(self, request, response, view):
        pass


class ThreadLocalApiRequestMiddleware(BaseApiMiddleware):

    def preprocess_request(self, request, view):
        _thread_locals.api_request = request

    def process_response(self, request, response, view):
        pass

    @staticmethod
    def get_current_request():
        return getattr(_thread_locals, 'api_request', None)

