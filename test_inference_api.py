#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Inference API Test Script

Usage:
1. Modify API_BASE_URL to your server address
2. Modify video_url and audio_url to your test file URLs
3. Run script: python test_inference_api.py
"""

import requests
import json
import time
import hashlib
import os
from datetime import datetime


# Configuration
API_BASE_URL = "http://localhost:8000"  # Change to your server address
INFERENCE_ENDPOINT = "/form/inference/"

# Test video and audio URLs - Please replace with your actual file URLs
TEST_VIDEO_URL = "https://example.com/test_video.mp4"  # Replace with actual video URL
TEST_AUDIO_URL = "https://example.com/test_audio.mp3"  # Replace with actual audio URL

# Optional: If authentication token is needed, set it here
# AUTH_TOKEN = "your_auth_token_here"

# Cache configuration
ENABLE_CACHE = True
CACHE_DIR = "./api_cache"


def get_cache_key(video_url, audio_url):
    """Generate cache key based on input URLs"""
    content = f"{video_url}_{audio_url}"
    return hashlib.md5(content.encode()).hexdigest()


def save_to_cache(cache_key, data):
    """Save API response to cache"""
    if not ENABLE_CACHE:
        return

    try:
        os.makedirs(CACHE_DIR, exist_ok=True)
        cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")

        cache_data = {
            'timestamp': time.time(),
            'data': data
        }

        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2)

        print(f"💾 Response cached to: {cache_file}")
    except Exception as e:
        print(f"⚠️  Cache save failed: {e}")


def load_from_cache(cache_key, max_age_hours=24):
    """Load API response from cache"""
    if not ENABLE_CACHE:
        return None

    try:
        cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")

        if not os.path.exists(cache_file):
            return None

        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)

        # Check cache age
        cache_age = time.time() - cache_data['timestamp']
        if cache_age > max_age_hours * 3600:
            print(f"🕐 Cache expired (age: {cache_age/3600:.1f}h), will make new request")
            return None

        print(f"🎯 Using cached response (age: {cache_age/60:.1f}min)")
        return cache_data['data']

    except Exception as e:
        print(f"⚠️  Cache load failed: {e}")
        return None


def call_inference_api(video_url, audio_url, auth_token=None, use_cache=True):
    """
    Call inference API with caching support

    Args:
        video_url: Video file URL
        audio_url: Audio file URL
        auth_token: Authentication token (optional)
        use_cache: Whether to use cache (default: True)

    Returns:
        dict: API response result
    """
    # Check cache first
    cache_key = get_cache_key(video_url, audio_url)
    if use_cache:
        cached_result = load_from_cache(cache_key)
        if cached_result is not None:
            return cached_result

    url = f"{API_BASE_URL}{INFERENCE_ENDPOINT}"

    # Request headers
    headers = {
        'Content-Type': 'application/json',
    }

    # Add auth token to headers if provided
    if auth_token:
        headers['Authorization'] = f'Token {auth_token}'

    # Request payload
    payload = {
        'video_url': video_url,
        'audio_url': audio_url
    }

    print(f"🚀 Sending inference request...")
    print(f"📋 Request URL: {url}")
    print(f"📦 Request data: {json.dumps(payload, indent=2)}")
    print("-" * 50)

    try:
        # Send POST request
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=300)  # 5 minutes timeout
        end_time = time.time()

        # Calculate duration
        duration = end_time - start_time

        print(f"⏱️  Request duration: {duration:.2f} seconds")
        print(f"📊 Response status code: {response.status_code}")

        # Parse response
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"📄 Response content:")
            print(json.dumps(response_data, indent=2))

            # Check business logic result
            if response_data.get('error_code') == 0:
                print("✅ Inference successful!")
                video_url = response_data.get('data', {}).get('video_url')
                if video_url:
                    print(f"🎬 Generated video URL: {video_url}")

                # Save successful result to cache
                if use_cache:
                    save_to_cache(cache_key, response_data)

                return response_data
            else:
                print(f"❌ Inference failed: {response_data.get('error_reason', 'Unknown error')}")
                return response_data
        else:
            print(f"📄 Response content (non-JSON): {response.text}")
            return {"error": "Non-JSON response", "content": response.text}

    except requests.exceptions.Timeout:
        print("⏰ Request timeout!")
        return {"error": "Request timeout"}
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error! Please check if server is running and URL is correct")
        return {"error": "Connection error"}
    except requests.exceptions.RequestException as e:
        print(f"❌ Request exception: {str(e)}")
        return {"error": f"Request exception: {str(e)}"}
    except json.JSONDecodeError:
        print(f"❌ JSON decode error, response content: {response.text}")
        return {"error": "JSON decode error", "content": response.text}
    except Exception as e:
        print(f"❌ Unknown error: {str(e)}")
        return {"error": f"Unknown error: {str(e)}"}


def main():
    """Main function"""
    print("=" * 60)
    print("🧪 Inference API Test Script")
    print(f"🕐 Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # Check if test URLs are configured
    if TEST_VIDEO_URL == "https://example.com/test_video.mp4":
        print("⚠️  Warning: Please modify TEST_VIDEO_URL to actual video file URL")

    if TEST_AUDIO_URL == "https://example.com/test_audio.mp3":
        print("⚠️  Warning: Please modify TEST_AUDIO_URL to actual audio file URL")

    print()

    # Call API
    result = call_inference_api(
        video_url=TEST_VIDEO_URL,
        audio_url=TEST_AUDIO_URL,
        # auth_token=AUTH_TOKEN  # Uncomment this line if authentication is needed
    )

    print("-" * 50)
    if result.get('error_code') == 0:
        print("🎉 Test completed: Success!")
    else:
        print("💥 Test completed: Failed!")

    return result


if __name__ == "__main__":
    # Run test
    result = main()

    # Optional: Save result to file
    # with open('test_result.json', 'w', encoding='utf-8') as f:
    #     json.dump(result, f, indent=2)
    #     print(f"\n📁 Test result saved to: test_result.json")
