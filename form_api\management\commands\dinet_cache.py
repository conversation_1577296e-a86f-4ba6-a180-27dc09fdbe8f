import os
from django.core.management.base import BaseCommand, CommandError
from form_api.tools import (
    clear_inference_cache, 
    get_inference_cache_stats,
    DINetCacheManager,
    CACHE_AVAILABLE
)


class Command(BaseCommand):
    help = 'DINet缓存管理专用命令'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['clear', 'stats', 'info', 'cleanup'],
            help='缓存操作: clear(清空), stats(统计), info(信息), cleanup(清理过期)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制执行操作，不询问确认'
        )
        parser.add_argument(
            '--cache-dir',
            default='./dinet_cache',
            help='缓存目录路径 (默认: ./dinet_cache)'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if not CACHE_AVAILABLE:
            raise CommandError('❌ 缓存功能不可用，请检查缓存模块安装')
        
        try:
            if action == 'clear':
                self.handle_clear(options)
            elif action == 'stats':
                self.handle_stats(options)
            elif action == 'info':
                self.handle_info(options)
            elif action == 'cleanup':
                self.handle_cleanup(options)
        except Exception as e:
            raise CommandError(f'缓存操作失败: {str(e)}')

    def handle_clear(self, options):
        """清空所有缓存"""
        if not options['force']:
            confirm = input('⚠️  确定要清空所有缓存吗? (y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write('❌ 操作已取消')
                return
        
        self.stdout.write('🗑️  正在清空缓存...')
        success = clear_inference_cache()
        
        if success:
            self.stdout.write(self.style.SUCCESS('✅ 缓存清空完成'))
        else:
            self.stdout.write(self.style.ERROR('❌ 缓存清空失败'))

    def handle_stats(self, options):
        """显示缓存统计信息"""
        self.stdout.write('📊 获取缓存统计信息...')
        
        stats = get_inference_cache_stats()
        if stats:
            self.stdout.write(self.style.SUCCESS('📈 缓存统计:'))
            self.stdout.write('-' * 40)
            
            for key, value in stats.items():
                if 'size' in key.lower() and isinstance(value, (int, float)):
                    # 格式化大小显示
                    if value > 1024 * 1024:
                        formatted_value = f"{value / (1024 * 1024):.2f} MB"
                    elif value > 1024:
                        formatted_value = f"{value / 1024:.2f} KB"
                    else:
                        formatted_value = f"{value} B"
                    self.stdout.write(f'   {key}: {formatted_value}')
                else:
                    self.stdout.write(f'   {key}: {value}')
        else:
            self.stdout.write(self.style.WARNING('⚠️  无法获取缓存统计信息'))

    def handle_info(self, options):
        """显示缓存系统信息"""
        cache_dir = options['cache_dir']
        
        self.stdout.write('ℹ️  DINet缓存系统信息:')
        self.stdout.write('-' * 40)
        self.stdout.write(f'   缓存目录: {os.path.abspath(cache_dir)}')
        self.stdout.write(f'   缓存可用: {"是" if CACHE_AVAILABLE else "否"}')
        
        if os.path.exists(cache_dir):
            # 计算目录大小
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
                        file_count += 1
            
            self.stdout.write(f'   目录存在: 是')
            self.stdout.write(f'   文件数量: {file_count}')
            self.stdout.write(f'   总大小: {total_size / (1024 * 1024):.2f} MB')
        else:
            self.stdout.write(f'   目录存在: 否')
        
        # 检查权限
        try:
            os.makedirs(cache_dir, exist_ok=True)
            test_file = os.path.join(cache_dir, '.test_permission')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            self.stdout.write(f'   目录权限: 可读写')
        except Exception as e:
            self.stdout.write(f'   目录权限: 错误 ({e})')

    def handle_cleanup(self, options):
        """清理过期缓存"""
        cache_dir = options['cache_dir']
        
        if not os.path.exists(cache_dir):
            self.stdout.write('⚠️  缓存目录不存在')
            return
        
        self.stdout.write('🧹 清理过期缓存...')
        
        try:
            cache_manager = DINetCacheManager(cache_dir=cache_dir)
            # 这里需要根据实际的缓存管理器实现来调用清理方法
            # cache_manager.cleanup_expired()
            
            self.stdout.write(self.style.SUCCESS('✅ 过期缓存清理完成'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ 清理失败: {e}'))