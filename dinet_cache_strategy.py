#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DINet Pipeline Cache Strategy Implementation

This module provides caching mechanisms for DINet pipeline to improve performance
by avoiding redundant computations for video preprocessing and audio feature extraction.
"""

import os
import pickle
import hashlib
import json
import time
import numpy as np
import torch
from typing import Optional, Dict, Any, Tuple
from pathlib import Path

# Django cache import
try:
    from django.core.cache import cache as django_cache
    DJANGO_CACHE_AVAILABLE = True
except ImportError:
    print("⚠️  Django cache not available, using file-only cache")
    django_cache = None
    DJANGO_CACHE_AVAILABLE = False


class DINetCacheManager:
    """
    Cache manager for DINet pipeline data

    Supports multiple cache backends:
    - File system cache for large data (video frames, features)
    - Redis cache for metadata and small data
    - Memory cache for frequently accessed data
    """

    def __init__(
        self,
        cache_dir: str = "./dinet_cache",
        enable_django_cache: bool = True,
        cache_timeout: int = 24 * 3600,  # 24 hours default
        max_memory_cache_size: int = 100
    ):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # File system cache directories
        self.video_cache_dir = self.cache_dir / "video_features"
        self.audio_cache_dir = self.cache_dir / "audio_features"
        self.video_cache_dir.mkdir(exist_ok=True)
        self.audio_cache_dir.mkdir(exist_ok=True)

        # Django cache
        self.django_cache = None
        self.cache_timeout = cache_timeout
        if enable_django_cache and DJANGO_CACHE_AVAILABLE:
            try:
                self.django_cache = django_cache
                # Test Django cache connection
                test_key = "dinet_cache_test"
                self.django_cache.set(test_key, "test_value", 10)
                test_result = self.django_cache.get(test_key)
                if test_result == "test_value":
                    self.django_cache.delete(test_key)
                    print("✅ Django cache connected successfully")
                else:
                    raise Exception("Django cache test failed")
            except Exception as e:
                print(f"⚠️  Django cache unavailable, using file cache only: {e}")
                self.django_cache = None

        # Memory cache (LRU-like)
        self.memory_cache = {}
        self.memory_cache_order = []
        self.max_memory_cache_size = max_memory_cache_size

    def _get_file_hash(self, file_path: str) -> str:
        """Generate hash for file content"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read(8192)  # Read first 8KB for hash
            file_stat = os.stat(file_path)
            content = f"{file_path}_{file_stat.st_size}_{file_stat.st_mtime}_{file_content}"
            return hashlib.md5(content.encode() if isinstance(content, str) else content).hexdigest()
        except Exception:
            # Fallback to path-based hash
            return hashlib.md5(file_path.encode()).hexdigest()

    def _get_cache_key(self, prefix: str, *args) -> str:
        """Generate cache key"""
        content = f"{prefix}_{'_'.join(str(arg) for arg in args)}"
        return hashlib.md5(content.encode()).hexdigest()

    def _save_to_file(self, file_path: Path, data: Any) -> bool:
        """Save data to file"""
        try:
            file_path.parent.mkdir(exist_ok=True)

            if isinstance(data, torch.Tensor):
                torch.save(data, file_path.with_suffix('.pt'))
            elif isinstance(data, np.ndarray):
                np.save(file_path.with_suffix('.npy'), data)
            else:
                with open(file_path.with_suffix('.pkl'), 'wb') as f:
                    pickle.dump(data, f)
            return True
        except Exception as e:
            print(f"File save error: {e}")
            return False

    def _load_from_file(self, file_path: Path) -> Optional[Any]:
        """Load data from file"""
        try:
            # Try different extensions
            for ext, loader in [
                ('.pt', torch.load),
                ('.npy', np.load),
                ('.pkl', lambda p: pickle.load(open(p, 'rb')))
            ]:
                full_path = file_path.with_suffix(ext)
                if full_path.exists():
                    return loader(full_path)
            return None
        except Exception as e:
            print(f"File load error: {e}")
            return None

    def _update_memory_cache(self, key: str, data: Any):
        """Update memory cache with LRU-like behavior"""
        if key in self.memory_cache:
            self.memory_cache_order.remove(key)

        self.memory_cache[key] = data
        self.memory_cache_order.append(key)

        # Remove oldest entries if cache is full
        while len(self.memory_cache) > self.max_memory_cache_size:
            oldest_key = self.memory_cache_order.pop(0)
            del self.memory_cache[oldest_key]

    # ========== Video Processing Cache ==========

    def cache_video_features(
        self,
        video_path: str,
        video_frames: torch.Tensor,
        boxes: list,
        affine_matrices: list,
        points_list: list,
        ref_idx: np.ndarray
    ) -> str:
        """Cache video preprocessing results"""
        file_hash = self._get_file_hash(video_path)
        cache_key = self._get_cache_key("video", file_hash)

        # Prepare cache data
        cache_data = {
            'video_frames': video_frames,
            'boxes': boxes,
            'affine_matrices': affine_matrices,
            'points_list': points_list,
            'ref_idx': ref_idx,
            'timestamp': time.time(),
            'source_path': video_path
        }

        # Save to file system
        cache_file = self.video_cache_dir / cache_key
        if self._save_to_file(cache_file, cache_data):
            # Save metadata to Django cache
            if self.django_cache:
                metadata = {
                    'type': 'video_features',
                    'source_path': video_path,
                    'cache_file': str(cache_file),
                    'timestamp': time.time(),
                    'size': len(video_frames)
                }
                try:
                    self.django_cache.set(
                        f"dinet_video_meta:{cache_key}",
                        metadata,
                        self.cache_timeout
                    )
                except Exception as e:
                    print(f"Django cache set error: {e}")

            print(f"💾 Video features cached: {cache_key}")
            return cache_key

        return None

    def load_video_features(self, video_path: str) -> Optional[Dict[str, Any]]:
        """Load cached video preprocessing results"""
        file_hash = self._get_file_hash(video_path)
        cache_key = self._get_cache_key("video", file_hash)

        # Check memory cache first
        if cache_key in self.memory_cache:
            print(f"🎯 Video features from memory cache")
            return self.memory_cache[cache_key]

        # Check file system
        cache_file = self.video_cache_dir / cache_key
        cached_data = self._load_from_file(cache_file)

        if cached_data:
            # Update memory cache
            self._update_memory_cache(cache_key, cached_data)
            print(f"📁 Video features from file cache")
            return cached_data

        return None

    # ========== Audio Processing Cache ==========

    def cache_audio_features(
        self,
        audio_path: str,
        whisper_feature: np.ndarray,
        whisper_chunks: list,
        fps: int = 25
    ) -> str:
        """Cache audio preprocessing results"""
        file_hash = self._get_file_hash(audio_path)
        cache_key = self._get_cache_key("audio", file_hash, fps)

        cache_data = {
            'whisper_feature': whisper_feature,
            'whisper_chunks': whisper_chunks,
            'fps': fps,
            'timestamp': time.time(),
            'source_path': audio_path
        }

        # Save to file system
        cache_file = self.audio_cache_dir / cache_key
        if self._save_to_file(cache_file, cache_data):
            # Save metadata to Django cache
            if self.django_cache:
                metadata = {
                    'type': 'audio_features',
                    'source_path': audio_path,
                    'cache_file': str(cache_file),
                    'timestamp': time.time(),
                    'fps': fps,
                    'chunks_count': len(whisper_chunks)
                }
                try:
                    self.django_cache.set(
                        f"dinet_audio_meta:{cache_key}",
                        metadata,
                        self.cache_timeout
                    )
                except Exception as e:
                    print(f"Django cache set error: {e}")

            print(f"💾 Audio features cached: {cache_key}")
            return cache_key

        return None

    def load_audio_features(self, audio_path: str, fps: int = 25) -> Optional[Dict[str, Any]]:
        """Load cached audio preprocessing results"""
        file_hash = self._get_file_hash(audio_path)
        cache_key = self._get_cache_key("audio", file_hash, fps)

        # Check memory cache first
        if cache_key in self.memory_cache:
            print(f"🎯 Audio features from memory cache")
            return self.memory_cache[cache_key]

        # Check file system
        cache_file = self.audio_cache_dir / cache_key
        cached_data = self._load_from_file(cache_file)

        if cached_data:
            # Update memory cache
            self._update_memory_cache(cache_key, cached_data)
            print(f"📁 Audio features from file cache")
            return cached_data

        return None

    # ========== Cache Management ==========

    def clear_cache(self, cache_type: str = "all"):
        """Clear cache data"""
        if cache_type in ["all", "video"]:
            for file in self.video_cache_dir.glob("*"):
                file.unlink()
            print("🗑️  Video cache cleared")

        if cache_type in ["all", "audio"]:
            for file in self.audio_cache_dir.glob("*"):
                file.unlink()
            print("🗑️  Audio cache cleared")

        if cache_type in ["all", "memory"]:
            self.memory_cache.clear()
            self.memory_cache_order.clear()
            print("🗑️  Memory cache cleared")

        if cache_type in ["all", "django"] and self.django_cache:
            try:
                # Clear Django cache keys with DINet prefix
                self.django_cache.delete_many([
                    key for key in self.django_cache._cache.keys()
                    if key.startswith('dinet_')
                ])
                print("🗑️  Django cache cleared")
            except AttributeError:
                # Fallback for different cache backends
                try:
                    # Try to clear all DINet-related keys by pattern
                    import re
                    # This is a simplified approach - in production you might want
                    # to keep track of cache keys separately
                    print("🗑️  Django cache clear attempted (limited support)")
                except Exception as e:
                    print(f"⚠️  Django cache clear failed: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        stats = {
            'video_cache_count': len(list(self.video_cache_dir.glob("*"))),
            'audio_cache_count': len(list(self.audio_cache_dir.glob("*"))),
            'memory_cache_count': len(self.memory_cache),
            'total_cache_size_mb': sum(
                f.stat().st_size for f in self.cache_dir.rglob("*") if f.is_file()
            ) / (1024 * 1024),
            'django_cache_available': self.django_cache is not None
        }

        if self.django_cache:
            try:
                # Try to get Django cache statistics
                # Note: Django cache doesn't provide direct size/key count methods
                # This is a simplified approach
                test_key = "dinet_stats_test"
                self.django_cache.set(test_key, "test", 1)
                if self.django_cache.get(test_key):
                    stats['django_cache_operational'] = True
                    self.django_cache.delete(test_key)
                else:
                    stats['django_cache_operational'] = False

                # If using Redis backend for Django cache, we can get more stats
                if hasattr(self.django_cache, '_cache') and hasattr(self.django_cache._cache, '_cache'):
                    backend_info = str(type(self.django_cache._cache))
                    stats['django_cache_backend'] = backend_info

            except Exception as e:
                stats['django_cache_error'] = str(e)

        return stats


# Usage example for DINet pipeline integration
class CachedDINetPipeline:
    """
    DINet Pipeline with integrated caching
    """

    def __init__(self, dinet, audio_encoder, cache_manager: DINetCacheManager, **kwargs):
        self.original_pipeline = None  # Original DINetPipeline instance
        self.cache_manager = cache_manager
        # ... other initialization

    def __call__(self, video_path: str, audio_path: str, **kwargs):
        """
        Main pipeline call with caching
        """
        # Try to load cached video features
        video_cache = self.cache_manager.load_video_features(video_path)
        if video_cache is None:
            # Process video and cache results
            video_frames, original_video_frames, boxes, affine_matrices, points_list = self.affine_transform_video(video_path)
            ref_idx = self.obtain_mouth_openness(points_list)

            self.cache_manager.cache_video_features(
                video_path, video_frames, boxes, affine_matrices, points_list, ref_idx
            )
        else:
            # Use cached data
            video_frames = video_cache['video_frames']
            boxes = video_cache['boxes']
            affine_matrices = video_cache['affine_matrices']
            points_list = video_cache['points_list']
            ref_idx = video_cache['ref_idx']
            print("🚀 Using cached video features, skipping preprocessing!")

        # Try to load cached audio features
        audio_cache = self.cache_manager.load_audio_features(audio_path, kwargs.get('video_fps', 25))
        if audio_cache is None:
            # Process audio and cache results
            whisper_feature = self.audio_encoder.audio2feat(audio_path)
            whisper_chunks = self.audio_encoder.feature2chunks(
                feature_array=whisper_feature,
                fps=kwargs.get('video_fps', 25)
            )

            self.cache_manager.cache_audio_features(
                audio_path, whisper_feature, whisper_chunks, kwargs.get('video_fps', 25)
            )
        else:
            # Use cached data
            whisper_feature = audio_cache['whisper_feature']
            whisper_chunks = audio_cache['whisper_chunks']
            print("🚀 Using cached audio features, skipping preprocessing!")

        # Continue with inference using cached/processed data
        # ... rest of the pipeline logic

        return "output_video_path"


if __name__ == "__main__":
    # Example usage
    cache_manager = DINetCacheManager(
        cache_dir="./dinet_cache",
        enable_django_cache=True,
        cache_timeout=24 * 3600  # 24 hours
    )

    # Print cache statistics
    stats = cache_manager.get_cache_stats()
    print("Cache Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
