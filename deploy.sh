#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置
ENV=${1:-"dev"}
VERSION=${2:-"latest"}

echo -e "${GREEN}🚀 开始部署 ${ENV} 环境，版本: ${VERSION}${NC}"

# 检查环境文件
if [ "$ENV" = "prod" ]; then
    if [ ! -f ".env.prod" ]; then
        echo -e "${RED}❌ 缺少 .env.prod 文件${NC}"
        exit 1
    fi
    COMPOSE_FILE="docker-compose.prod.yml"
    ENV_FILE=".env.prod"
else
    COMPOSE_FILE="docker-compose.yml"
    ENV_FILE=".env"
fi

# 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker-compose -f $COMPOSE_FILE down

# 拉取最新镜像（如果使用远程镜像）
if [ "$ENV" = "prod" ]; then
    echo -e "${YELLOW}📥 拉取最新镜像...${NC}"
    docker-compose -f $COMPOSE_FILE pull
fi

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 10

# 运行数据库迁移
echo -e "${YELLOW}🗄️  运行数据库迁移...${NC}"
docker-compose -f $COMPOSE_FILE exec web python manage.py migrate

# 收集静态文件（生产环境）
if [ "$ENV" = "prod" ]; then
    echo -e "${YELLOW}📁 收集静态文件...${NC}"
    docker-compose -f $COMPOSE_FILE exec web python manage.py collectstatic --noinput
fi

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
docker-compose -f $COMPOSE_FILE ps

# 健康检查
echo -e "${YELLOW}🏥 健康检查...${NC}"
sleep 5
if curl -f http://localhost:8000/watchman/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务健康检查通过！${NC}"
else
    echo -e "${RED}❌ 服务健康检查失败${NC}"
    docker-compose -f $COMPOSE_FILE logs web
    exit 1
fi

echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${YELLOW}📊 查看日志: docker-compose -f $COMPOSE_FILE logs -f${NC}"
echo -e "${YELLOW}🔧 进入容器: docker-compose -f $COMPOSE_FILE exec web bash${NC}"