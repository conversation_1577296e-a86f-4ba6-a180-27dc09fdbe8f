# syntax=docker/dockerfile:1

FROM python:3.10 as base

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    default-libmysqlclient-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    ffmpeg \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制requirements文件
COPY requirements.txt dinet_requirements.txt ./

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r dinet_requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /opt/logs/tangyinx
RUN mkdir -p /tmp

# 设置权限
RUN chmod +x manage.py

# 暴露端口
EXPOSE 8000

# # 健康检查
# HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
#     CMD curl -f http://localhost:8000/watchman/ || exit 1

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "300", "dinet_kouxing_api.wsgi:application"]