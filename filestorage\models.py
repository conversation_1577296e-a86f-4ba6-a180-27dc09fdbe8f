import os
import oss2
from django.db import models
from django.conf import settings


OSS_ACCESS_KEY_ID = settings.OSS_ACCESS_KEY_ID
OSS_ACCESS_KEY_SECRET = settings.OSS_ACCESS_KEY_SECRET
OSS_ENDPOINT = settings.OSS_ENDPOINT
OSS_BUCKET_NAME = settings.OSS_BUCKET_NAME
OSS_BASE_PATH = settings.OSS_BASE_PATH


# 素材类型选择
MEDIA_TYPE_VIDEO = 'video'
MEDIA_TYPE_AUDIO = 'audio'

MEDIA_TYPE_CHOICES = [
    (MEDIA_TYPE_VIDEO, '视频'),
    (MEDIA_TYPE_AUDIO, '音频'),
]


class OSSFileStorage(models.Model):
    path = models.CharField(max_length=200, verbose_name="file path")
    data = models.BinaryField(verbose_name='file binary data')
    size = models.IntegerField(default=0, blank=True)

    # 新增字段
    user_id = models.Integer<PERSON>ield(default=0, verbose_name="用户ID")
    username = models.CharField(max_length=150, default='', blank=True, verbose_name="用户名")
    media_type = models.CharField(max_length=10, choices=MEDIA_TYPE_CHOICES, default=MEDIA_TYPE_VIDEO, verbose_name="素材类型")
    original_name = models.CharField(max_length=255, default='', blank=True, verbose_name="原始文件名")
    description = models.TextField(blank=True, default='', verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'oss_file_storage'
        verbose_name = '文件存储'
        verbose_name_plural = '文件存储'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_id', 'media_type']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.original_name or self.path} - {self.get_media_type_display()}'

    @property
    def url(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket.sign_url('GET', self.oss_path, 1*60*60)

    @property
    def oss_path(self):
        oss_path = os.path.join(OSS_BASE_PATH, self.path)
        return oss_path


    def save(self, **kwargs):
        # 这个方法特别危险，第二次save的时候，会把oss上的文件清空！
        # 理论上这个model不允许二次修改，触发save！
        self.oss_upload()
        return super().save(**kwargs)


    def delete(self, **kwargs):
        self.oss_delete()
        return super().delete(**kwargs)


    def oss_upload(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        oss_real_path = os.path.join(OSS_BASE_PATH, self.path)
        bucket.put_object(oss_real_path, self.data)
        self.data = b''
        return self.path

    def oss_delete(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        oss_real_path = os.path.join(OSS_BASE_PATH, self.path)
        result = bucket.delete_object(oss_real_path)
        return oss_real_path


