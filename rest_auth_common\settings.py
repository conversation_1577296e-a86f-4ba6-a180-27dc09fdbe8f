from rest_framework.settings import APISettings
from django.conf import settings
import os

ACCESS_TOKEN_TIMEOUT_TS = os.environ.get('ACCESS_TOKEN_TIMEOUT_TS', 600)
LOCK_USER_TIMEOUT_TS = os.environ.get('LOCK_USER_TIMEOUT_TS', 1800)

USER_SETTINGS = getattr(settings, 'REST_CAPTCHA', None)
ALLOW_MULTI_SIGN_ON = getattr(settings, 'ALLOW_MULTI_SIGN_ON', False)

FONT_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 'fonts/Vera.ttf')

MASTER_CAPTCHA_KEY = os.environ.get("MASTER_CAPTCHA_KEY", None)
MASTER_CAPTCHA_VALUE = os.environ.get("MASTER_CAPTCHA_VALUE", None)
MASTER_CAPTCHA = {}
if MASTER_CAPTCHA_KEY and MASTER_CAPTCHA_VALUE:
    MASTER_CAPTCHA = {MASTER_CAPTCHA_KEY: MASTER_CAPTCHA_VALUE}

DEFAULTS = {
    'CAPTCHA_CACHE': 'default',
    'CAPTCHA_TIMEOUT': 300,  # 5 minuts
    'CAPTCHA_CACHE_KEY': 'rest_captcha_{key}.{version}',
    'CAPTCHA_KEY': 'captcha_key',
    'CAPTCHA_IMAGE': 'captcha_image',
    'CAPTCHA_LENGTH': 4,
    'CAPTCHA_FONT_PATH': FONT_PATH,
    'CAPTCHA_FONT_SIZE': 22,
    'CAPTCHA_IMAGE_SIZE': (120, 32),
    'CAPTCHA_LETTER_ROTATION': (-35, 35),
    'CAPTCHA_FOREGROUND_COLOR': '#001100',
    'CAPTCHA_BACKGROUND_COLOR': '#ffffff',
    'FILTER_FUNCTION': 'rest_auth_common.captcha.filter_default',
    'NOISE_FUNCTION': 'rest_auth_common.captcha.noise_default',
    # for tests access: MASTER_CAPTCHA: {'secret_key: secret_value'}
    'MASTER_CAPTCHA': MASTER_CAPTCHA,

    'ACCESS_TOKEN_TIMEOUT_TS': ACCESS_TOKEN_TIMEOUT_TS,
    'LOCK_USER_TIMEOUT_TS': LOCK_USER_TIMEOUT_TS,
}

ERROR_MESSAGES = {
    'wrong_captcha': 'Captcha cache timeout or wrong key',
    'captcha_error': 'Captcha code error',
    'user_locked': 'Locked user {{ username }}',
    'wrong_password': 'password is wrong for user {{ username }}',
    'eaccess_token_not_match': 'app_id, app_secret, access_token, username does not match',
}

CUSTOM_ERROR_MESSAGES = getattr(settings, 'REST_AUTH_COMMON_CUSTOM_ERROR_MESSAGES', {})
ERROR_MESSAGES.update(CUSTOM_ERROR_MESSAGES)

MESSAGE_TITLE = getattr(settings, 'REST_AUTH_COMMON_CUSTOM_MESSAGE_TITLE', 'error_reason')
CODE_TITLE = getattr(settings, 'REST_AUTH_COMMON_CUSTOM_CODE_TITLE', 'error_code')

# List of settings that may be in string import notation.
IMPORT_STRINGS = ('FILTER_FUNCTION', 'NOISE_FUNCTION')

api_settings = APISettings(USER_SETTINGS, DEFAULTS, IMPORT_STRINGS)

SINGLE_SIGN_ON_ONLY = int(os.environ.get('SINGLE_SIGN_ON_ONLY', 1))
