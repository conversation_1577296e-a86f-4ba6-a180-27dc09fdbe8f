import os
import json
import pytz
import string
import random
import datetime
import traceback
import requests
from urllib.parse import urljoin

from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User
from django.conf import settings
from django_redis import get_redis_connection
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from rest_framework.response import Response
from rest_framework import status, exceptions

from common_rest.views import CustomizedAPIView, expose
from rest_auth_common.utils import decrypt_pwd
from rest_auth_common.models import AutoExpiredAuthToken

from utils.standard_response import format_response
from . import models, serializers, filters

import logging

logger = logging.getLogger('common.logger')

class AccountView(CustomizedAPIView):
    model = models.Account
    serializer_class = serializers.AccountSerializer
    filter_class = filters.AccountFilter
    lookup_field = "id"

    @format_response
    def get(self, request):
        return self._i_list(request).data

    @transaction.atomic
    def post(self, request):
        '''
        {
            'username': '',
            'password': '',
            'name': '',
            'phone_number': '',
        }
        :param request:
        :return:
        '''
        try:
            username = request.data['username']
            password = request.data['password']
            validate_password(password)
            user_obj = User.objects.create_user(username=username, password=password)
        except ValidationError:
            return Response({'error_code': 1,'error_reason': '密码不符合规范'})
        except:
            logger.error(traceback.format_exc())
            return Response({'error_code': 1,'error_reason': '创建账号出现错误'})

        account_name = request.data.get('name', '')
        phone_number = request.data.get('phone_number', '')

        new_acc_data = {
            'user_id': user_obj.id,
            'name': account_name,
            'phone_number': phone_number,
        }
        new_acc_obj = self.model.objects.create(**new_acc_data)
        return Response({
            'error_code': 0,
            'error_reason': 'ok',
            'data': self.get_item(new_acc_obj)
        })

    def put(self, request):  # 这个接口不修改密码!
        try:
            account_obj = self.get_object()
            if not account_obj.user:
                return Response({'error_code': 1,'error_reason': 'user不存在'})
        except self.model.DoesNotExist:
            return Response({'error_code': 1,'error_reason': 'account不存在'})
        data = request.data.copy()
        ser = self.get_serializer(instance=account_obj, data=data, partial=True)
        if not ser.is_valid():
            return self.serializer_error_response(ser)

        ser.save()
        return Response({'error_code': 0,'error_reason': 'ok', 'data': ser.data})

    def delete(self, request):
        acc_obj = self.get_object()
        data = self.get_item(acc_obj)
        acc_obj.user.delete()
        return Response({'error_code': 0,'error_reason': 'ok', 'data': data})


    @expose(['GET'])
    def info(self, request):
        token = request.query_params['token']  # auto expired auth token
        auth_token_obj = AutoExpiredAuthToken.object.get(key=token)
        user = auth_token_obj.user
        account_id = 0
        if hasattr(user, 'account'):
            account_obj = user.account
            account_id = account_obj.id
        return Response({
            'user_id': user.id,
            'username': user.username,
            'key': token,
            'account_id': account_id,
        })

    @expose(['GET'])
    def logout(self, request):
        user = request.user
        token = AutoExpiredAuthToken.objects.get(user_id=user.id)
        key = token.key
        token.expire_time = datetime.datetime.today() - datetime.timedelta(days=1)
        token.save()
        return Response({'error_code': 0,'error_reason': 'ok'})


    @expose(['POST'])
    def change_self_rsa_password(self, request):
        user = request.user
        rsa_key_id = request.data["rsa_key_id"]
        auth_data = request.data['auth_data']
        auth_data_dict = json.loads(decrypt_pwd(rsa_key_id, auth_data))
        new_pwd = auth_data_dict['password']
        try:
            validate_password(new_pwd)
        except ValidationError:
            return Response({'error_code': 1,'error_reason': '密码不符合规范'})
        try:
            changed_user = user
            changed_user.set_password(new_pwd)
            changed_user.save()
            return Response({'error_code': 0, 'error_reason': 'ok'})
        except self.model.DoesNotExist:
            return Response({'error_code': 1, 'error_reason': '用户信息或权限有误'})


    @expose(['POST'])
    def change_account_rsa_password(self, request):
        rsa_key_id = request.data["rsa_key_id"]
        auth_data = request.data['auth_data']
        auth_data_dict = json.loads(decrypt_pwd(rsa_key_id, auth_data))
        account_id= auth_data_dict['account_id']
        try:
            account_obj = self.model.objects.get(pk=account_id)
            if not account_obj.user:
                return Response({'error_code': 1,'error_reason': 'user不存在'})
        except self.model.DoesNotExist:
            return Response({'error_code': 1,'error_reason': 'account不存在'})

        new_pwd = auth_data_dict['password']
        try:
            validate_password(new_pwd)
        except ValidationError:
            return Response({'error_code': 1,'error_reason': '密码不符合规范'})
        try:
            changed_user = account_obj.user
            changed_user.set_password(new_pwd)
            changed_user.save()
            return Response({'error_code': 0, 'error_reason': 'ok'})
        except self.model.DoesNotExist:
            return Response({'error_code': 1, 'error_reason': '用户信息或权限有误'})

