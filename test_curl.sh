#!/bin/bash
# Inference API curl test script

# Configuration - Please modify according to actual situation
API_URL="http://localhost:8000/form/inference/"
VIDEO_URL="https://your-domain.com/test_video.mp4"  # Please replace with actual video URL
AUDIO_URL="https://your-domain.com/test_audio.mp3"  # Please replace with actual audio URL

echo "=========================================="
echo "Inference API curl Test"
echo "=========================================="
echo "API URL: $API_URL"
echo "Video URL: $VIDEO_URL"
echo "Audio URL: $AUDIO_URL"
echo "------------------------------------------"

# Send POST request
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "'"$VIDEO_URL"'",
    "audio_url": "'"$AUDIO_URL"'"
  }' \
  --max-time 300 \
  --verbose

echo ""
echo "=========================================="
echo "Test completed"
echo "=========================================="
