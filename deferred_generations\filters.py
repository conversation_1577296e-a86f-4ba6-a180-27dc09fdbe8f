import django_filters
from django_filters.rest_framework.filterset import FilterSet

from . import models


class DeferredGenerationTaskFilter(FilterSet):
    o = django_filters.OrderingFilter(
        fields=('pk', '-pk', 'created_at', '-created_at', 'updated_at', '-updated_at'))

    class Meta:
        model = models.DeferredGenerationTask
        fields = {
            "id": ['exact', ],
            'user_id': ['exact', ],
            'username': ['exact', 'icontains'],
            'source_audio': ['icontains', ],
            'source_video': ['icontains', ],
            'generated_video_path': ['icontains', ],
        }
