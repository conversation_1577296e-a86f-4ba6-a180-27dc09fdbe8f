import os
import sys


def add_package_libs_from_root_package_init(init_file):
    add_package_libs_from_root_package_location(os.path.dirname(init_file))


def add_package_libs_from_root_package_location(cur_dir):
    _curdir = os.path.abspath(cur_dir)
    _path = os.path.join(_curdir, 'libs')
    append_lib_to_syspath(_path)
    for _file in os.listdir(os.path.join(_curdir, 'libs')):
        if _file.endswith('.zip'):
            _path = os.path.join(_curdir, 'libs', _file)
            append_lib_to_syspath(_path)


def append_lib_to_syspath(libpath):
    if libpath not in sys.path:
        sys.path.append(libpath)
