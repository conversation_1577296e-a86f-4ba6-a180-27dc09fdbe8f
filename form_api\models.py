import os
import oss2
from django.db import models
from django.conf import settings


OSS_ACCESS_KEY_ID = settings.OSS_ACCESS_KEY_ID
OSS_ACCESS_KEY_SECRET = settings.OSS_ACCESS_KEY_SECRET
OSS_ENDPOINT = settings.OSS_ENDPOINT
OSS_BUCKET_NAME = settings.OSS_BUCKET_NAME
OSS_BASE_PATH = settings.OSS_BASE_PATH


class OUTPUTVIDEOOSSFileStorage(models.Model):
    path = models.CharField(max_length=200, verbose_name="file path")
    data = models.BinaryField(verbose_name='file binary data')
    size = models.IntegerField(default=0, blank=True)

    @property
    def url(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket.sign_url('GET', self.oss_path, 1*60*60)

    @property
    def oss_path(self):
        oss_path = os.path.join(OSS_BASE_PATH, self.path)
        return oss_path

    def save(self, **kwargs):
        # 这个方法特别危险，第二次save的时候，会把oss上的文件清空！
        # 理论上这个model不允许二次修改，触发save！
        self.oss_upload()
        return super().save(**kwargs)

    def delete(self, **kwargs):
        self.oss_delete()
        return super().delete(**kwargs)

    def oss_upload(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        oss_real_path = os.path.join(OSS_BASE_PATH, self.path)
        bucket.put_object(oss_real_path, self.data)
        self.data = b''
        return self.path

    def oss_delete(self):
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        oss_real_path = os.path.join(OSS_BASE_PATH, self.path)
        result = bucket.delete_object(oss_real_path)
        return oss_real_path


