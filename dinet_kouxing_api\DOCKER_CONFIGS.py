import os

DJANGO_REQ_ERROR_LOG = "/opt/logs/tangyinx/req_error.log"
DEBUG_LOG = "/opt/logs/tangyinx/debug.log"
COMMON_REST_LOG = "/opt/logs/tangyinx/common_rest.log"

DEBUG = False
ALLOWED_HOSTS = ['*']

DATABASES = {
    'default': {
        'ENGINE': "django.db.backends.mysql",
        'NAME': os.environ["MYSQL_DATABASE"],
        'USER': os.environ["MYSQL_USER"],
        'PASSWORD': os.environ["MYSQL_PASSWORD"],
        'HOST': os.environ["DB_HOST"],
        'PORT': "",
        'CONN_MAX_AGE': 60,
        'ATOMIC_REQUESTS': False,
        'AUTOCOMMIT': True,
        'OPTIONS': {},
    },
}

redis_location = "redis://%s:6379/1" % os.environ["REDIS_HOST"]
if os.environ.get("REDIS_PASSWORD"):
    redis_location = "redis://:%s@%s:6379/1" % (os.environ["REDIS_PASSWORD"], os.environ["REDIS_HOST"]),
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': redis_location,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PICKLE_VERSION': -1,
            'SOCKET_TIMEOUT': 5,
            'IGNORE_EXCEPTIONS': True,
        }
    }
}
