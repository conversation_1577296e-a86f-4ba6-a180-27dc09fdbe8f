# -*- coding: utf-8 -*-


import httpagentparser


# 注册QQ浏览器
class QQBrowser(httpagentparser.Browser):
    name = 'QQ浏览器'
    look_for = 'QQBrowser'
    order = 0

    def getVersion(self, agent, word):
        version_string = agent.split(word, 1)[1]
        return version_string[1:]


class MSIE(httpagentparser.MSIE):
    look_for = "MSIE"
    skip_if_found = ["Opera"]
    name = "IE"
    version_markers = [" ", ";"]
    order = 0


class Trident(httpagentparser.Trident):
    name = "IE"
    order = 0


httpagentparser.detectorshub.register(QQBrowser())
httpagentparser.detectorshub.register(MSIE())
httpagentparser.detectorshub.register(Trident())
