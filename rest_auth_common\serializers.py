# -*- coding: utf-8 -*-

from rest_framework import serializers

from .models import AutoExpiredAuthToken


class AutoExpiredAuthTokenSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField()
    expire_in = serializers.IntegerField(read_only=True)

    class Meta:
        model = AutoExpiredAuthToken
        fields = ('user_id', 'key', 'expire_time', 'expire_in')
        read_only_fields = ('user_id', 'key', 'expire_time', 'expire_in')

    def is_valid(self):
        return self.object.expire_in > 0
