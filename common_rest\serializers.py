# -*- coding: utf-8 -*-

import copy
import json
import base64
import sys

import six
from django.utils.module_loading import import_string
from django.utils.translation import ugettext_lazy as _

from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from .exceptions import ParameterError
from .relations import CustomizedPrimaryKeyRelatedField

from utils_common.variables import VAR
from utils_common.exchange_image import get_image_bytes_from_base64
from .versioning import check_api_version_gte
from . import serializers_patch  # 不要删除这一行


class DeprecatedProcessor(object):
    def __init__(self, instance, context=None, extras=None, extra_name=None, **kwargs):
        pass

    @property
    def data(self):
        return None


class _AllWrapper(object):
    def __init__(self, obj):
        self.obj = obj

    def all(self):
        return self.obj


def many_serializer_getter_wrapper(func):
    def wrapper(*args, **kwargs):
        return _AllWrapper(func(*args, **kwargs))

    return wrapper


def safe_getter(obj, name):
    return getattr(obj, name, None)


class SimpleSerializer(serializers.BaseSerializer):
    def __init__(self, instance, *args, **kwargs):
        self.instance = instance

    # def to_representation(self, instance):
    #     return instance

    @property
    def data(self):
        return self.instance


class ExtraRelationSerializers(serializers.ModelSerializer):
    serializer_related_field = CustomizedPrimaryKeyRelatedField

    def __init__(self, *args, **kwargs):
        self.extras = kwargs.pop('extras', {})
        fields = kwargs.pop('fields', None)
        excludes = kwargs.pop('excludes', None)
        super(ExtraRelationSerializers, self).__init__(*args, **kwargs)
        if fields:
            allowed = set(fields)
            existing = set(self.fields.keys())
            for field_name in existing - allowed:
                self.fields.pop(field_name)
        if excludes:
            for field_name in excludes:
                self.fields.pop(field_name)

    def get_fields(self):
        if '_fields_class_cache' not in self.__class__.__dict__:
            self.__class__._fields_class_cache = super().get_fields()
        return copy.copy(self.__class__._fields_class_cache)

    def to_representation(self, instance):
        request = self._context.get('request', None)
        ret = serializers.ModelSerializer.to_representation(self, instance)
        for extra in self.extras:
            try:
                processor = getattr(self.Meta, 'extra__' + extra)
            except AttributeError:
                raise ParameterError("Can't get extra %s processor for serializer %s" % (extra, self.__class__))
            if isinstance(processor, VAR):
                if hasattr(processor, 'permission'):
                    has_permission = processor.permission().has_permission(self._context['request'], self._context['view'])
                    if not has_permission:
                        raise PermissionDenied("You aren't grant to access %s from serializer %s" % (extra, self.__class__))
                serializer_class, getter, many = processor.serializer, processor.getter, processor.many
                if isinstance(serializer_class, six.string_types):
                    serializer_class = import_string(serializer_class)
                elif serializer_class is None:
                    serializer_class = SimpleSerializer
                if getter is None:
                    getter = safe_getter
                if many:
                    data = [serializer_class(instance=obj, context=self._context, extras=self.extras.get(extra, {})).data
                            if obj is not None else {} for obj in getter(instance, extra).all()]
                else:
                    obj = getter(instance, extra)
                    if obj is None:
                        if check_api_version_gte(request, 1.1):
                            data = None
                        else:
                            data = {}
                    else:
                        data = serializer_class(instance=obj, context=self._context, extras=self.extras.get(extra, {})).data
            elif isinstance(processor, six.string_types):
                processor_class = import_string(processor)
                data = processor_class(instance, context=self._context, extras=self.extras.get(extra, {}), extra_name=extra).data
            else:
                data = processor(instance, context=self._context, extras=self.extras.get(extra, {}), extra_name=extra).data
            ret[extra] = data
        return ret

    def build_field(self, field_name, info, model_class, nested_depth):
        """
        Return a two tuple of (cls, kwargs) to build a serializer field with.
        """
        if field_name in info.fields_and_pk:
            model_field = info.fields_and_pk[field_name]
            return self.build_standard_field(field_name, model_field)

        elif field_name.endswith('_id') and field_name[:-3] == info.pk.name:
            _field_name = field_name[:-3]
            model_field = info.fields_and_pk[_field_name]
            return self.build_standard_field(_field_name, model_field)

        elif field_name in info.relations:
            relation_info = info.relations[field_name]
            if not nested_depth:
                return self.build_relational_field(field_name, relation_info)
            else:
                return self.build_nested_field(field_name, relation_info, nested_depth)

        elif field_name.endswith('_id') and field_name[:-3] in info.forward_relations:
            return self.build_foreign_key_field(field_name, info, model_class)

        elif hasattr(model_class, field_name):
            return self.build_property_field(field_name, model_class)

        elif field_name == self.url_field_name:
            return self.build_url_field(field_name, model_class)

        return self.build_unknown_field(field_name, model_class)

    def build_foreign_key_field(self, field_name, info, model_class):
        """
        Set all ForeignKey's serializer field class to CustomedPrimaryKeyRelatedField.
        """
        _field_name = field_name[:-3]
        relation_info = info.relations[_field_name]
        return self.build_relational_field(_field_name, relation_info)

    def get_default_field_names(self, declared_fields, model_info):
        """
        Set ForeignKey and OneToOneField field_name to field_name + '_id' and exclude ManyToManyField field_name.
        """
        pk_field_name = model_info.pk.name
        if isinstance(model_info.pk, serializers.models.OneToOneField):
            pk_field_name = pk_field_name + '_id'
        return (
                [pk_field_name] +
                list(declared_fields.keys()) +
                list(model_info.fields.keys()) +
                [field_name + '_id' for field_name in model_info.forward_relations.keys() if not model_info.forward_relations[field_name].to_many]
        )


class Base64ListField(serializers.CharField):
    # 相比TextJSONField存储, 能够节约传输和存储空间
    # 写入的时候, 统一使用base64来存储
    # 读取的时候, 根据request.query_params['feature_format']来判断返回值类型
    #   - base64: 返回base64
    #   - list: 返回list
    #   - 其他时候, 返回json.dumps(list)
    # 读取的时候, 兼容历史条件下存储为json string格式的数据

    BIG_ENDIAN = 'big'
    LITTLE_ENDIAN = 'little'

    def __init__(self, **kwargs):
        """

        :param args:
        :param kwargs:
            - dtype: 数组类型
            - byteorder: 大小端, 无论传输还是存储, 都应该保证使用这个格式, 默认使用大端
        """
        import numpy as np
        self.dtype = kwargs.pop('dtype', np.float32)
        self.byteorder = kwargs.pop('byteorder', self.BIG_ENDIAN)
        super().__init__(**kwargs)

    def list_to_base64(self, data):
        import numpy as np
        np_array = np.asarray(data, dtype=self.dtype)
        if sys.byteorder != self.byteorder:
            np_array = np_array.byteswap()
        ret = base64.b64encode(np_array)
        return ret

    def base64_to_list(self, data):
        import numpy as np
        np_array = np.frombuffer(base64.b64decode(data), dtype=self.dtype)
        if sys.byteorder != self.byteorder:
            np_array = np_array.byteswap()
        ret = np_array.tolist()
        return ret

    def feature_to_base64(self, data):
        if isinstance(data, list):
            return self.list_to_base64(data)
        elif isinstance(data, str):
            if data.startswith('['):
                data = json.loads(data)
                return self.list_to_base64(data)
            else:
                return data.encode()
        elif isinstance(data, bytes):
            return data
        else:
            raise NotImplementedError("Unknown data type %s" % type(data))

    def feature_to_list(self, data):
        if isinstance(data, list):
            return data
        elif isinstance(data, str):
            if data.startswith('['):
                return json.loads(data)
            else:
                return self.base64_to_list(data.encode())
        elif isinstance(data, bytes):
            return self.base64_to_list(data)
        else:
            raise NotImplementedError("Unknown data type %s" % type(data))

    def feature_to_json_str(self, data):
        if isinstance(data, list):
            return json.dumps(data)
        elif isinstance(data, str):
            if data.startswith('['):
                return data
            else:
                return json.dumps(self.base64_to_list(data.encode()))
        elif isinstance(data, bytes):
            return json.dumps(self.base64_to_list(data))
        else:
            raise NotImplementedError("Unknown data type %s" % type(data))

    def to_representation(self, value):
        try:
            request = self.parent._context['request']
            feature_format = request.query_params['feature_format']
        except KeyError:
            feature_format = None
        if feature_format == 'base64':
            return self.feature_to_base64(value).decode()
        elif feature_format == 'list':
            return self.feature_to_list(value)
        else:
            return self.feature_to_json_str(value)

    def to_internal_value(self, data):
        # 写入的时候
        # 需要转化为str写入
        # 空值存储为空列表
        if not data:
            data = []
        return self.feature_to_base64(data).decode()


class TextJSONField(serializers.CharField):

    def __init__(self, *args, **kwargs):
        self.top_type = kwargs.pop('top_type', 'dict')
        super(TextJSONField, self).__init__(*args, **kwargs)

    def to_representation(self, value):
        if not value:
            if self.top_type == 'dict':
                value = '{}'
            elif self.top_type == 'list':
                value = '[]'
            else:
                raise NotImplementedError("Unknown type %s" % self.top_type)
        return json.loads(value)

    def to_internal_value(self, data):
        if not data:
            if self.top_type == 'dict':
                return '{}'
            elif self.top_type == 'list':
                return '[]'
            else:
                raise NotImplementedError("Unknown type %s" % self.top_type)
        if isinstance(data, six.string_types):
            return data
        else:
            return json.dumps(data)


class Base64ImageField(serializers.Field):
    default_error_messages = {
        'required': _('No data was submitted.'),
        'invalid': _('The submitted data was not a base64 str. Check the encoding type on the form.real type:{type}'),
        'empty': _('The submitted data is empty.'),
        'decode_error': _('decode error when save image,error: {error}'),
        'max_length': _('Ensure this filename has at most {max_length} characters (it has {length}).'),
    }

    def to_representation(self, value):
        return base64.b64encode(value).decode("ascii")

    def to_internal_value(self, data):
        if not data and not self.allow_null:
            self.fail('required')
        if not isinstance(data, six.string_types):
            self.fail('invalid', type=type(data))
        try:
            return get_image_bytes_from_base64(data)[1]
        except Exception as e:
            self.fail('decode_error', error=str(e))
