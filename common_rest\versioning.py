# coding=utf-8


import logging

logger = logging.getLogger('common.logger')


def get_api_version(request):
    if request is not None:
        # 如果settings里面未配置, request.version 返回None, float会导致报错
        api_version = request.version or 1.0
    else:
        logger.warning("request值为空, 无法判定版本", exc_info=True)
        api_version = 1.0
    return float(api_version)


def check_api_version_gte(request, version):
    request_version = get_api_version(request)
    return request_version + 0.0001 > version