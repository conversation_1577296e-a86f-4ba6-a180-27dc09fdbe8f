import os
import argparse
import include

import django

django.setup()

from omegaconf import OmegaConf
from django.conf import settings
from form_api.tools import inference

dinet_config = OmegaConf.load(settings.DINET_CONFIG_PATH)
inference_ckpt_path = settings.INFERENCE_CKPT_PATH

parser = argparse.ArgumentParser(description='latent sync runner')
parser.add_argument('--video', dest='src_video', help='source video', default='')
parser.add_argument('--audio', dest='src_audio', help='source audio', default='')
parser.add_argument('--out', dest='output_path', help='output video', default='')
args = parser.parse_args()


def process_task(local_video_input, local_audio_input, output_path):
    result_path = inference(
        config=dinet_config,
        video_path=local_video_input,
        audio_path=local_audio_input,
        video_out_path=output_path,
        inference_ckpt_path=inference_ckpt_path,
        enable_cache=False  # 禁用缓存
    )
    return result_path


if __name__ == '__main__':
    process_task(args.src_video, args.src_audio, args.output_path)
