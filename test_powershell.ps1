# Inference API PowerShell Test Script

# Configuration - Please modify according to actual situation
$API_URL = "http://localhost:8000/form/inference/"
$VIDEO_URL = "https://your-domain.com/test_video.mp4"  # Please replace with actual video URL
$AUDIO_URL = "https://your-domain.com/test_audio.mp3"  # Please replace with actual audio URL

Write-Host "==========================================" -ForegroundColor Green
Write-Host "Inference API PowerShell Test" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "API URL: $API_URL"
Write-Host "Video URL: $VIDEO_URL"
Write-Host "Audio URL: $AUDIO_URL"
Write-Host "------------------------------------------"

# Construct request data
$requestBody = @{
    video_url = $VIDEO_URL
    audio_url = $AUDIO_URL
} | ConvertTo-Json

Write-Host "Sending request data:" -ForegroundColor Yellow
Write-Host $requestBody

try {
    # Send POST request
    $response = Invoke-RestMethod -Uri $API_URL -Method Post -Body $requestBody -ContentType "application/json" -TimeoutSec 300

    Write-Host "------------------------------------------"
    Write-Host "Response result:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 10 | Write-Host

    # Check result
    if ($response.error_code -eq 0) {
        Write-Host "✅ Success!" -ForegroundColor Green
        if ($response.data.video_url) {
            Write-Host "Generated video URL: $($response.data.video_url)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ Failed: $($response.error_reason)" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "==========================================" -ForegroundColor Green
Write-Host "Test completed" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green