# -*- coding: utf-8 -*-


import functools
import logging

import six
from django.core.cache import caches, DEFAULT_CACHE_ALIAS, InvalidCacheBackendError


if six.PY2:
    _function_code_attr = 'func_code'
    _function_name_attr = 'func_name'
    _function_defaults_attr = 'func_defaults'
else:
    _function_code_attr = '__code__'
    _function_name_attr = '__name__'
    _function_defaults_attr = '__defaults__'

if six.PY2:
    _cache_key_types = (basestring, int, long)
else:
    _cache_key_types = (str, int)

logger = logging.getLogger('common.logger')


def default_cache_key_func(cache_prefix, *args, **kwargs):
    cache_list_value = list(args) + [kwargs[_] for _ in sorted(kwargs.keys())]
    for i in range(len(cache_list_value)):
        if cache_list_value[i] is not None and not isinstance(cache_list_value[i], _cache_key_types):
            raise TypeError(
                'Cache arguments only support (basestring, int, long, NoneType) %s %s' % (
                    cache_list_value[i], type(cache_list_value[i]))
            )
        cache_list_value[i] = str(cache_list_value[i])
    cache_key = '%s_%s' % (cache_prefix, '_'.join(cache_list_value))
    return cache_key


def default_cache_timeout_func():
    return 3600


def with_cache(cache_prefix, get_cache_key_func=default_cache_key_func, timeout=default_cache_timeout_func,
               cache_alias=DEFAULT_CACHE_ALIAS):
    try:
        cache = caches[cache_alias]
    except InvalidCacheBackendError:
        logger.warning('cache alias not found, alias name: %s', cache_alias)
        cache = caches[DEFAULT_CACHE_ALIAS]

    def decorator(raw_function):
        _co_args_count = getattr(raw_function, _function_code_attr).co_argcount
        _co_named_var = getattr(raw_function, _function_code_attr).co_varnames[:_co_args_count]

        # 判断是否有默认参数, 如果有, 返回默认参数是从第几个开始
        _co_default = getattr(raw_function, _function_defaults_attr)
        if _co_default is not None:
            _has_default = True
            _co_default_start = _co_args_count - len(_co_default)
        else:
            _has_default = False

        # 判断是否需要对第一个参数做特殊处理
        if _co_named_var and _co_named_var[0] in ['self', 'cls']:
            _check_self = True
        else:
            _check_self = False

        def get_cache_key(*args, **kwargs):
            cache_kwargs = kwargs.copy()

            # 前_co_args_count个数据, 以list的形式传到make_key函数
            cache_args = list(args[:])
            for idx in range(len(cache_args), _co_args_count):
                var_name = _co_named_var[idx]
                if var_name in cache_kwargs:
                    cache_args.append(cache_kwargs.pop(var_name))
                elif _has_default:
                    idx_in_default = idx - _co_default_start
                    if idx_in_default < 0:
                        raise TypeError(
                            '%s takes at least %s arguments your arguments is : (args: %s; kwargs : %s) ' % (
                                getattr(raw_function, _function_name_attr), _co_args_count, args, kwargs))
                    cache_args.append(_co_default[idx_in_default])
                else:
                    raise TypeError('%s takes at least %s arguments your arguments is : (args: %s; kwargs : %s) ' % (
                        getattr(raw_function, _function_name_attr), _co_args_count, args, kwargs))

            # 如果check_self是True, args的长度至少为1

            if _check_self and not isinstance(cache_args[0], _cache_key_types):
                cache_args = list(cache_args[1:])  # 类方法 或者 对象方法
            else:
                cache_args = list(cache_args[:])
            cache_key = get_cache_key_func(cache_prefix, *cache_args, **cache_kwargs)
            return cache_key

        @functools.wraps(raw_function)
        def wrapper(*args, **kwargs):
            cache_key = get_cache_key(*args, **kwargs)
            cache_ret = cache.get(cache_key)
            if cache_ret is None:
                raw_ret = raw_function(*args, **kwargs)
                cache_ret = raw_ret
                if callable(timeout):
                    timeout_val = timeout()
                else:
                    timeout_val = timeout
                cache.set(cache_key, cache_ret, timeout_val)
            return cache_ret

        def refresh_cache(*args, **kwargs):
            cache_key = get_cache_key(*args, **kwargs)
            cache.delete(cache_key)

        wrapper.refresh_cache = refresh_cache

        return wrapper

    return decorator
