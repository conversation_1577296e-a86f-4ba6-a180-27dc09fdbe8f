#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified Cache Configuration for DINet Pipeline

This file provides basic caching functionality when the full cache strategy is not available.
"""

import os
import json
import time
import hashlib
from pathlib import Path


class SimpleCacheManager:
    """
    Simplified cache manager for DINet pipeline
    Uses only file system caching for basic functionality
    """

    def __init__(self, cache_dir: str = "./dinet_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # Create cache subdirectories
        self.video_cache_dir = self.cache_dir / "video_features"
        self.audio_cache_dir = self.cache_dir / "audio_features"
        self.video_cache_dir.mkdir(exist_ok=True)
        self.audio_cache_dir.mkdir(exist_ok=True)

        print(f"📁 Simple cache initialized: {cache_dir}")

    def _get_file_hash(self, file_path: str) -> str:
        """Generate hash for file"""
        try:
            stat = os.stat(file_path)
            content = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(content.encode()).hexdigest()
        except Exception:
            return hashlib.md5(file_path.encode()).hexdigest()

    def _get_cache_key(self, prefix: str, *args) -> str:
        """Generate cache key"""
        content = f"{prefix}_{'_'.join(str(arg) for arg in args)}"
        return hashlib.md5(content.encode()).hexdigest()

    def load_audio_features(self, audio_path: str):
        """Try to load cached audio features"""
        if not os.path.exists(audio_path):
            return None

        file_hash = self._get_file_hash(audio_path)
        cache_key = self._get_cache_key("audio", file_hash)
        cache_file = self.audio_cache_dir / f"{cache_key}.json"

        if cache_file.exists():
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    if time.time() - data.get('timestamp', 0) < 24 * 3600:  # 24 hours
                        print(f"💾 Found cached audio features: {cache_key[:8]}...")
                        return data
            except Exception as e:
                print(f"Cache load error: {e}")

        return None

    def cache_audio_features(self, audio_path: str, features_data: dict) -> str:
        """Cache audio features"""
        try:
            file_hash = self._get_file_hash(audio_path)
            cache_key = self._get_cache_key("audio", file_hash)
            cache_file = self.audio_cache_dir / f"{cache_key}.json"

            cache_data = {
                'features': features_data,
                'timestamp': time.time(),
                'source_path': audio_path
            }

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, default=str)

            print(f"💾 Audio features cached: {cache_key[:8]}...")
            return cache_key
        except Exception as e:
            print(f"Cache save error: {e}")
            return None

    def clear_cache(self, cache_type: str = "all"):
        """Clear cache files"""
        try:
            count = 0
            if cache_type in ["all", "audio"]:
                for file in self.audio_cache_dir.glob("*.json"):
                    file.unlink()
                    count += 1

            if cache_type in ["all", "video"]:
                for file in self.video_cache_dir.glob("*.json"):
                    file.unlink()
                    count += 1

            print(f"🗑️  Cleared {count} cache files")
            return True
        except Exception as e:
            print(f"Cache clear error: {e}")
            return False

    def get_cache_stats(self) -> dict:
        """Get cache statistics"""
        try:
            audio_files = list(self.audio_cache_dir.glob("*.json"))
            video_files = list(self.video_cache_dir.glob("*.json"))

            total_size = sum(f.stat().st_size for f in audio_files + video_files)

            return {
                'audio_cache_count': len(audio_files),
                'video_cache_count': len(video_files),
                'memory_cache_count': 0,
                'total_cache_size_mb': total_size / (1024 * 1024),
                'cache_type': 'simple'
            }
        except Exception as e:
            print(f"Stats error: {e}")
            return {}


# Make SimpleCacheManager available as DINetCacheManager for compatibility
DINetCacheManager = SimpleCacheManager
