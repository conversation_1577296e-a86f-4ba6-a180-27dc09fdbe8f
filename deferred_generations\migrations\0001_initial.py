# Generated by Django 3.2 on 2025-07-30 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DeferredGenerationTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(blank=True, default=0)),
                ('username', models.CharField(blank=True, default='', max_length=500)),
                ('source_audio', models.URLField(max_length=1000)),
                ('source_video', models.URLField(max_length=1000)),
                ('generated_video', models.URLField(blank=True, default='', max_length=1000)),
                ('status', models.CharField(choices=[('waiting', 'Waiting'), ('inference', 'In progress'), ('finished', 'Finished')], default='waiting', max_length=32)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
