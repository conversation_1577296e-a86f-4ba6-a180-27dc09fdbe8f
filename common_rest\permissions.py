# -*- coding: utf-8 -*-


from django.conf import settings, ImproperlyConfigured
from django.utils.module_loading import import_string
from django.utils.functional import cached_property
import six
from rest_framework.permissions import AllowAny, BasePermission

METHOD_SET = {'GET', 'PUT', 'POST', 'HEAD', 'DELETE', 'OPTIONS', 'TRACE', 'CONNECT'}


class IsActiveUser(BasePermission):
    """
    Allows access only to active users.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_active


class IsSuperuser(BasePermission):
    """
    Allows access only to super users.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_active and request.user.is_superuser


def is_in_group(group_name):
    class _IsInGroupPermission(BasePermission):
        """
        Allows access only to user in certain group
        """

        def has_permission(self, request, view):
            user = request.user
            return user and user.is_active and user.groups.filter(name=group_name).exists()

    return _IsInGroupPermission


class CustomPermission(object):
    def __init__(self, custom_permissions_conf):
        self._custom_permissions_conf = custom_permissions_conf

    @staticmethod
    def find_permission(permission_dict, view, method, action):
        if view not in permission_dict:
            return None
        if not isinstance(permission_dict[view], dict):
            return permission_dict[view]
        if action is None:
            return permission_dict[view].get(method, None)
        if action not in permission_dict[view]:
            return None
        if not isinstance(permission_dict[view][action], dict):
            return permission_dict[view][action]
        return permission_dict[view][action].get(method, None)

    @staticmethod
    def set_permission(permission_dict, view, method=None, action=None, permission=None):
        if view not in permission_dict:
            permission_dict[view] = {}
        if action is None:
            if method is None:
                permission_dict[view] = permission
                return
            else:
                permission_dict[view][method] = permission
                return
        if action not in permission_dict[view]:
            permission_dict[view][action] = {}
        if method is None:
            permission_dict[view][action] = permission
            return
        else:
            permission_dict[view][action][method] = permission
            return

    @staticmethod
    def set_view_permission(permission_dict, view, method_action_permission_dict):
        if view in permission_dict and not isinstance(permission_dict[view], dict) and isinstance(method_action_permission_dict, dict):
            raise ImproperlyConfigured("You should set simple permission after complex permission for view %s!" % view)
        if isinstance(method_action_permission_dict, type) and issubclass(method_action_permission_dict, BasePermission):
            CustomPermission.set_permission(permission_dict, view=view, method=None, action=None, permission=method_action_permission_dict)
            return
        elif not isinstance(method_action_permission_dict, dict):
            raise ImproperlyConfigured("You should set permission with dict or BasePermission's subclass for view %s" % view)
        for method_or_action in method_action_permission_dict:
            if method_or_action in METHOD_SET:
                permission = method_action_permission_dict[method_or_action]
                if isinstance(method_action_permission_dict, type) and not issubclass(permission, BasePermission):
                    raise ImproperlyConfigured(
                        "You should set permission with dict or BasePermission's subclass for view %s method %s" % (view, method_or_action))
                CustomPermission.set_permission(permission_dict, view, method_or_action, action=None, permission=permission)
                continue
            action_permission_dict = method_action_permission_dict[method_or_action]
            if not isinstance(action_permission_dict, dict):
                if isinstance(action_permission_dict, type) and issubclass(action_permission_dict, BasePermission):
                    CustomPermission.set_permission(permission_dict, view, method=None, action=method_or_action, permission=action_permission_dict)
                else:
                    raise ImproperlyConfigured(
                        "You should set action permission with dict or BasePermission's subclass for view %s action %s" % (view, method_or_action))
            else:
                if view in permission_dict and method_or_action in permission_dict[view] and not isinstance(permission_dict[view][method_or_action], dict):
                    raise ImproperlyConfigured(
                        "You should set simple permission after complex permission for view %s and action %s!" % (view, method_or_action))
                for method in action_permission_dict:
                    if isinstance(method_action_permission_dict, type) and not issubclass(action_permission_dict[method], BasePermission):
                        raise ImproperlyConfigured(
                            "You should set permission with BasePermission's subclass for view %s method %s action %s" % (view, method, method_or_action))
                    CustomPermission.set_permission(permission_dict, view, method, method_or_action, action_permission_dict[method])

    @staticmethod
    def set_permission_list(permission_dict, permission_list):
        for permission_item in permission_list:
            if isinstance(permission_item, (list, tuple)):
                if len(permission_item) != 2:
                    raise ImproperlyConfigured("Permission Item should be 2 size list %s" % permission_item)
                view, method_action_permission_dict = permission_item
                CustomPermission.set_view_permission(permission_dict, view, method_action_permission_dict)
            else:
                permission_list = import_string(permission_item)
                CustomPermission.set_permission_list(permission_dict, permission_list)

    @cached_property
    def custom_permissions_conf(self):
        if self._custom_permissions_conf is None:
            return
        if isinstance(self._custom_permissions_conf, six.string_types):
            return import_string(self._custom_permissions_conf)
        raise ImproperlyConfigured("custom permission conf %s should be string or None" % self._custom_permissions_conf)

    @cached_property
    def all_custom_permissions(self):
        _all_custom_permissions = {}
        if self.custom_permissions_conf:
            CustomPermission.set_permission_list(_all_custom_permissions, self.custom_permissions_conf)
        return _all_custom_permissions

    def get_custom_permission_class(self, url_name, method_name, action_name):
        if self.custom_permissions_conf is None:
            return
        if action_name is not None and method_name.upper() == 'OPTIONS':
            return AllowAny
        return CustomPermission.find_permission(self.all_custom_permissions, url_name, method_name, action_name)


CUSTOM_PERMISSION = CustomPermission(getattr(settings, 'CUSTOM_PERMISSIONS_CONF', None))
