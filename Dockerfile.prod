# syntax=docker/dockerfile:1

# 构建阶段
FROM python:3.10 as builder

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    default-libmysqlclient-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# 复制requirements文件
COPY requirements.txt dinet_requirements.txt ./

# 创建虚拟环境并安装依赖
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r dinet_requirements.txt

# 生产阶段
FROM python:3.9-slim as production

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PATH="/opt/venv/bin:$PATH"

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    default-libmysqlclient-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN adduser --disabled-password --gecos "" --uid 10001 appuser

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

WORKDIR /app

# 复制项目文件
COPY --chown=appuser:appuser . .

# 创建必要的目录
RUN mkdir -p /opt/logs/tangyinx && chown -R appuser:appuser /opt/logs/tangyinx
RUN mkdir -p /tmp && chown -R appuser:appuser /tmp

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# # 健康检查
# HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
#     CMD curl -f http://localhost:8000/watchman/ || exit 1

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "300", "dinet_kouxing_api.wsgi:application"]