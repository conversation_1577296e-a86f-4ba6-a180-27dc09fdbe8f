"""
Django settings for dinet_kouxing_api project.

Generated by 'django-admin startproject' using Django 3.2.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import os
from pathlib import Path
from django.contrib import messages

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = Path(__file__).resolve().parent.parent

DJANGO_REQ_ERROR_LOG = os.path.join(BASE_DIR, "req_error.log")
DEBUG_LOG = os.path.join(BASE_DIR, "debug.log")
COMMON_REST_LOG = os.path.join(BASE_DIR, "common_rest.log")

DINET_CONFIG_PATH = os.path.join(BASE_DIR, "form_api/configs/unet/second_stage_dinet_clip.yaml")
INFERENCE_CKPT_PATH = os.path.join(BASE_DIR, "checkpoints/checkpoint-244500.pt")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-$tz*k!$ggeg^@mm5-p8hpd7put)w57!t_(qc6x=8_*m7pr^(^r'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    'corsheaders',
    'watchman',
    'rest_auth_common',

    'account_center',
    'filestorage',
    # 'form_api',
    'deferred_generations',
]

MIGRATION_MODULES = {
}

REST_FRAMEWORK = {
    'EXCEPTION_HANDLER': 'common_rest.views.exception_handler',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'common_rest.authentications.SessionAuthentication',  # 添加会话认证
        'rest_auth_common.token_auth_authentication.TokenAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': ('rest_framework.permissions.AllowAny',),
    'DEFAULT_RENDERER_CLASSES': ('rest_framework.renderers.JSONRenderer',),
    'DEFAULT_FILTER_BACKENDS': ('django_filters.rest_framework.DjangoFilterBackend',),
    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.QueryParameterVersioning',
    'VERSION_PARAM': 'api_version',
    'DEFAULT_THROTTLE_RATES': {
        'phone_throttle': {"second": 1, "minute": 7, "day": 50},
        'ip_throttle': {"second": 100},
    }
}

CUSTOM_PERMISSIONS_CONF = 'dinet_kouxing_api.permissions.CUSTOM_PERMISSIONS'

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

]

# API_MIDDLEWARE_CLASSES = [
#     'account_center.middleware.HTTPREFERERMiddleware',
# ]

ROOT_URLCONF = 'dinet_kouxing_api.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'dinet_kouxing_api.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = False

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 生成视频的默认输出目录
GENERATED_VIDEO_DIR = os.path.join(BASE_DIR, 'media', 'generated_videos')

# 确保媒体目录存在
os.makedirs(MEDIA_ROOT, exist_ok=True)
os.makedirs(GENERATED_VIDEO_DIR, exist_ok=True)

# 消息框架
MESSAGE_TAGS = {
    messages.DEBUG: 'debug',
    messages.INFO: 'info',
    messages.SUCCESS: 'success',
    messages.WARNING: 'warning',
    messages.ERROR: 'danger',
}

# 登录相关
LOGIN_URL = 'login'
LOGIN_REDIRECT_URL = 'task_list'
LOGOUT_REDIRECT_URL = 'login'

# 会话配置
SESSION_COOKIE_AGE = 1209600  # 2周
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = True

CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_HEADERS = (
    'content-disposition', 'accept-encoding',
    'content-type', 'accept', 'origin', 'authorization',
)
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


def update_configuration():
    try:
        if os.environ.get("TANGYINX_DOCKER"):
            from . import DOCKER_CONFIGS as _configs
        else:
            from . import CONFIGS as _configs
    except ImportError:
        _configs = None
    if _configs is None:
        return
    global_variables = globals()
    for setting in dir(_configs):
        if setting == setting.upper():
            setting_value = getattr(_configs, setting)
            global_variables[setting] = setting_value
    global_variables["SERVER_EMAIL"] = global_variables.get(
        "EMAIL_HOST_USER", "<EMAIL>")
    global_variables["TEMPLATE_DEBUG"] = global_variables.get("DEBUG", False)
    global_variables["MANAGERS"] = global_variables.get("ADMINS", ())


update_configuration()

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(asctime)s %(levelname)-8s[%(filename)s:%(lineno)d(%(funcName)s)] %(message)s'
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue'
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'filters': ['require_debug_true'],
            'class': 'logging.StreamHandler',
        },
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler',
        },
        'django_req_error_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': DJANGO_REQ_ERROR_LOG,
            'mode': 'a',
            'maxBytes': 100 * 1024 * 1024,
            'backupCount': 5,
            'encoding': 'utf-8',
            'formatter': 'verbose',
        },
        'common_rest_log_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': COMMON_REST_LOG,
            'mode': 'a',
            'maxBytes': 100 * 1024 * 1024,
            'backupCount': 5,
            'encoding': 'utf-8',
            'formatter': 'verbose',
        },
        'debug_log_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': DEBUG_LOG,
            'mode': 'a',
            'maxBytes': 100 * 1024 * 1024,
            'backupCount': 5,
            'encoding': 'utf-8',
            'formatter': 'verbose',
        },
        'sentry': {
            'level': 'INFO',
            'filters': ['require_debug_false'],
            'class': 'raven.contrib.django.raven_compat.handlers.SentryHandler',
        }
    },
    'loggers': {
        'django.request': {
            'handlers': ['console', 'django_req_error_handler'],
            # ['mail_admins', 'sentry'] using sentry to track 500 error setup when wsgi starts
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['console', 'sentry'],
            # ['mail_admins', 'sentry'] using sentry to track security  error setup when wsgi starts
            'level': 'WARNING',
            'propagate': False,
        },
        'rest.logger': {
            'handlers': ['common_rest_log_handler', 'sentry', 'console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'common.logger': {
            'handlers': ['debug_log_handler', 'sentry', 'console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django_auth_ldap': {
            'handlers': ['debug_log_handler', 'sentry', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

OSS_ACCESS_KEY_ID = os.environ.get("OSS_ACCESS_KEY_ID", "")
OSS_ACCESS_KEY_SECRET= os.environ.get("OSS_ACCESS_KEY_SECRET", "")
OSS_REGION = "cn-shanghai"
OSS_ARN = os.environ.get("OSS_ARN", "")
OSS_ENDPOINT = os.environ.get("OSS_ENDPOINT", "")
OSS_BUCKET_NAME = os.environ.get("OSS_BUCKET_NAME", "")
OSS_BASE_PATH = os.environ.get("OSS_BASE_PATH", "")

ALLOWED_REFERER_LIST = os.environ.get("ALLOWED_REFERER_LIST", "")
ALLOWED_REFERER_LIST = ALLOWED_REFERER_LIST.split("|")
ALLOWED_REFERER_LIST = [x for x in ALLOWED_REFERER_LIST if x]

# follow up test
SYSTEM_ENV = os.environ.get("SYSTEM_ENV", "PROD")  # TEST or PROD
