from rest_framework.permissions import IsAdminUser, AllowAny, IsAuthenticated

CUSTOM_PERMISSIONS = (
    (
        'filestorage.file',
        {
            'GET': IsAuthenticated,
            'POST': IsAuthenticated,
            'update_load': {'POST': IsAuthenticated},
            'm_update_load': {'POST': AllowAny},
            'delete_path': {'POST': IsAuthenticated},
            'get_sts_token': {'GET': AllowAny},
        }
    ),
)
