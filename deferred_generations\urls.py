from django.urls import path, re_path
from . import views, web_views


urlpatterns = [
    # API 接口
    re_path(r'^((?P<action>(batch_create|retry))/)?$',
            views.DeferredGenerationTaskView.as_view(), name='deferred.task'),

    # Web 页面路由
    path('web/', web_views.TaskListView.as_view(), name='task_list'),
    path('web/statistics/', web_views.task_statistics, name='task_statistics'),
    path('web/task/<int:task_id>/', web_views.task_detail_ajax, name='task_detail'),
    path('web/download/<int:task_id>/', web_views.download_result, name='download_result'),
]
