from rest_framework import serializers


class InferenceRequestSerializer(serializers.Serializer):
    video_url = serializers.URLField(required=True, help_text="视频URL")
    audio_url = serializers.URLField(required=True, help_text="音频URL")

    def validate_video_url(self, value):
        """验证视频URL格式"""
        if not value:
            raise serializers.ValidationError("视频URL不能为空")
        return value

    def validate_audio_url(self, value):
        """验证音频URL格式"""
        if not value:
            raise serializers.ValidationError("音频URL不能为空")
        return value


class InferenceResponseSerializer(serializers.Serializer):
    video_url = serializers.URLField(help_text="处理后的视频URL")
