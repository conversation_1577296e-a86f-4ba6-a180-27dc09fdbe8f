from common_rest.serializers import ExtraRelationSerializers, TextJSO<PERSON>ield
from rest_framework.serializers import <PERSON><PERSON><PERSON><PERSON>, IntegerField, <PERSON><PERSON><PERSON><PERSON><PERSON>

from . import models



class AccountSerializer(ExtraRelationSerializers):
    username = <PERSON><PERSON><PERSON><PERSON>(read_only=True)
    phone_number = <PERSON><PERSON><PERSON><PERSON>(required=False, allow_null=True, allow_blank=True)
    name = CharField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = models.Account
        fields = '__all__'
        read_only_fields = ['user_id', ]
