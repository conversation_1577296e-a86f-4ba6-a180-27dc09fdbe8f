# coding: utf-8


from django.core.exceptions import ObjectDoesNotExist
from rest_framework.relations import PrimaryKeyRelatedField, PKOnlyObject


class CustomizedPrimaryKeyRelatedField(PrimaryKeyRelatedField):
    def run_validators(self, data):
        try:
            self.get_queryset().get(pk=data)
        except ObjectDoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)
        return super(CustomizedPrimaryKeyRelatedField, self).run_validators(data)

    def to_internal_value(self, data):
        return data

    def get_attribute(self, instance):
        try:
            value = getattr(instance, self.source_attrs[-1])
            return PKOnlyObject(pk=value)
        except AttributeError:
            return None
