# -*- coding: utf-8 -*-


import os
import sys
import importlib

import django
from django.conf import settings, ENVIRONMENT_VARIABLE
from django.test.utils import get_runner

from pymodm.connection import _CONNECTIONS, ConnectionInfo
from mongomock.mongo_client import MongoClient


def init_test_cache():
    from django.core.cache.backends.locmem import Loc<PERSON>em<PERSON><PERSON>

    def lock(self, *args, **kwargs):
        class Context(object):
            def __init__(self, *args, **kwargs):
                pass

            def __enter__(self, *args, **kwargs):
                return None

            def __exit__(self, *args, **kwargs):
                return None

        return Context(*args, **kwargs)

    LocMemCache.lock = lock


def init_mongo_mock_connect(django_settings):
    _CONNECTIONS.clear()
    for i in django_settings.TEST_MONGO_DB_NAMES:
        _CONNECTIONS[i] = ConnectionInfo(
            parsed_uri='test_mongo_url',
            conn_string='test_mongo_url',
            database=getattr(MongoClient(), i))


def get_module():
    module_name = os.environ.get('django_app_test_target_module', None) or sys.argv[-1]
    module = importlib.import_module(module_name)
    return module, module_name


def django_config(module, module_name):
    base_settings_module = 'utils_common.django_app_test_settings'
    os.environ[ENVIRONMENT_VARIABLE] = base_settings_module
    test_settings = importlib.import_module(base_settings_module)
    test_settings.DEBUG = True
    if getattr(module, 'CUSTOM_PERMISSIONS_CONF', None) is not None:
        test_settings.CUSTOM_PERMISSIONS_CONF = module.CUSTOM_PERMISSIONS_CONF
    test_settings.ROOT_URLCONF = "%s.test_urls" % module_name
    test_settings.REST_FRAMEWORK = {
        'TEST_REQUEST_DEFAULT_FORMAT': 'json',
    }
    if hasattr(module, 'TEST_INSTALLED_APPS'):
        test_settings.INSTALLED_APPS = module.TEST_INSTALLED_APPS
        test_settings.MIGRATION_MODULES = {
            app[app.rfind('.') + 1:]: None
            for app in test_settings.INSTALLED_APPS
        }

    if getattr(module, 'CUSTOM_SETTINGS', None) is not None:
        for k, v in list(module.CUSTOM_SETTINGS.items()):
            setattr(test_settings, k, v)

    init_mongo_mock_connect(test_settings)
    init_test_cache()
    django.setup()


def run(module, module_name):
    TestRunner = get_runner(settings, 'django_nose.NoseTestSuiteRunner')
    test_runner = TestRunner()
    failures = test_runner.run_tests(
        getattr(module, 'TEST_ALL_CASES', [module_name]),
    )
    return failures


def main():
    module, module_name = get_module()
    django_config(module, module_name)
    failures = run(module, module_name)
    sys.exit(bool(failures))


if __name__ == '__main__':
    main()
