#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DINet Inference API Integration Example with Caching

This example shows how to integrate the caching functionality
into the existing inference API workflow.
"""

import os
import sys
from pathlib import Path

# Add project to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from form_api.tools import inference, clear_inference_cache, get_inference_cache_stats


def enhanced_inference_workflow(video_url, audio_url, output_path, config, checkpoint_path):
    """
    Enhanced inference workflow with caching integration

    This function demonstrates how to use the caching functionality
    in a real API workflow scenario.
    """

    print("🎬 Enhanced DINet Inference Workflow")
    print("=" * 45)

    # Step 1: Get cache statistics before processing
    print("📊 Current cache status:")
    initial_stats = get_inference_cache_stats()
    if initial_stats:
        for key, value in initial_stats.items():
            print(f"   {key}: {value}")
    else:
        print("   No cache available or cache empty")

    print()

    # Step 2: Run inference with caching enabled
    print("🚀 Running inference with caching...")
    try:
        # Enable caching by default for performance
        result_path = inference(
            config=config,
            video_path=video_url,  # In real API this would be downloaded file path
            audio_path=audio_url,  # In real API this would be downloaded file path
            video_out_path=output_path,
            inference_ckpt_path=checkpoint_path,
            seed=1247,
            enable_cache=True  # Enable caching for performance
        )

        print(f"✅ Inference completed successfully")
        print(f"📁 Output video: {result_path}")

    except Exception as e:
        print(f"❌ Inference failed: {e}")
        return None

    # Step 3: Display cache statistics after processing
    print("\n📊 Cache status after processing:")
    final_stats = get_inference_cache_stats()
    if final_stats:
        for key, value in final_stats.items():
            print(f"   {key}: {value}")

        # Calculate cache usage change
        if initial_stats:
            cache_growth = final_stats.get('total_cache_size_mb', 0) - initial_stats.get('total_cache_size_mb', 0)
            if cache_growth > 0:
                print(f"   Cache growth: +{cache_growth:.2f} MB")

    return result_path


def cache_management_examples():
    """Examples of cache management operations"""

    print("\n🔧 Cache Management Examples")
    print("=" * 30)

    # Example 1: Check cache stats
    print("1️⃣  Checking cache statistics...")
    stats = get_inference_cache_stats()
    if stats:
        print("   Cache statistics:")
        for key, value in stats.items():
            print(f"     {key}: {value}")

        # Recommend cache cleanup if size is large
        cache_size_mb = stats.get('total_cache_size_mb', 0)
        if cache_size_mb > 100:  # 100MB threshold
            print(f"   ⚠️  Cache size ({cache_size_mb:.1f} MB) is large, consider cleanup")
    else:
        print("   No cache statistics available")

    # Example 2: Cache cleanup
    print("\n2️⃣  Cache cleanup example...")
    print("   Before cleanup:")
    pre_cleanup_stats = get_inference_cache_stats()
    if pre_cleanup_stats:
        print(f"     Total size: {pre_cleanup_stats.get('total_cache_size_mb', 0):.2f} MB")
        print(f"     Total files: {pre_cleanup_stats.get('audio_cache_count', 0) + pre_cleanup_stats.get('video_cache_count', 0)}")

    # Uncomment the following line to actually clear cache
    # clear_inference_cache()
    print("   (Cache clearing disabled in example - uncomment to enable)")

    # Example 3: Performance monitoring
    print("\n3️⃣  Performance monitoring setup...")
    print("   You can monitor cache effectiveness by:")
    print("   - Tracking inference times with/without cache")
    print("   - Monitoring cache hit rates")
    print("   - Watching cache size growth")


def api_integration_example():
    """Example of how to integrate caching into the main API view"""

    print("\n🔗 API Integration Example")
    print("=" * 25)

    # This is a conceptual example of how to modify views.py
    example_code = '''
# In form_api/views.py - InferenceView class

def _process_inference(self, video_path, audio_path, video_out_path):
    """Enhanced inference processing with caching"""

    try:
        # Get configuration
        config = self._get_inference_config()
        checkpoint_path = self._get_checkpoint_path()

        # Enable caching for better performance
        # This will automatically use cached audio/video features when available
        result_path = inference(
            config=config,
            video_path=video_path,
            audio_path=audio_path,
            video_out_path=video_out_path,
            inference_ckpt_path=checkpoint_path,
            seed=1247,
            enable_cache=True  # 🚀 Enable caching!
        )

        # Optional: Log cache statistics for monitoring
        cache_stats = get_inference_cache_stats()
        if cache_stats:
            logger.info(f"Cache stats: {cache_stats}")

        return result_path

    except Exception as e:
        logger.error(f"Inference failed: {e}")
        raise

# Optional: Add cache management endpoints

class CacheManagementView(APIView):
    """API endpoints for cache management"""

    def get(self, request):
        """Get cache statistics"""
        stats = get_inference_cache_stats()
        return Response({
            'status': 'success',
            'data': stats or {}
        })

    def delete(self, request):
        """Clear cache"""
        success = clear_inference_cache()
        return Response({
            'status': 'success' if success else 'error',
            'message': 'Cache cleared' if success else 'Cache clear failed'
        })
'''

    print("Example integration code:")
    print(example_code)


if __name__ == "__main__":
    print("🎯 DINet Caching Integration Examples")
    print("=" * 40)

    # Show cache management examples
    cache_management_examples()

    # Show API integration example
    api_integration_example()

    print("\n📝 Usage Summary:")
    print("1. Import caching functions from form_api.tools")
    print("2. Enable caching in inference() calls with enable_cache=True")
    print("3. Monitor cache with get_inference_cache_stats()")
    print("4. Clean cache when needed with clear_inference_cache()")
    print("5. Cache will automatically speed up repeated operations")

    print("\n🎉 Integration examples complete!")
    print("\nNext steps:")
    print("- Update your views.py to enable caching")
    print("- Test with actual video/audio files")
    print("- Monitor cache performance in production")
    print("- Set up periodic cache cleanup if needed")
