# -*- coding: utf-8 -*-


import os
from pymodm.connection import DEFAULT_CONNECTION_ALIAS

BASE_DIR = os.path.abspath(os.getcwd())

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.8/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '$r$!6*#rp(m^0b5z==b9+_o-te%s*f79(fohfl4bm9kd-09rnw'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}

TEST_MONGO_DB_NAMES = {
    DEFAULT_CONNECTION_ALIAS,
    'fitting_room_material',
    'read',
    'write'
}

STATIC_URL = '/static/'

DATETIME_INPUT_FORMATS = [
    '%Y-%m-%dT%H:%M:%S',
    '%Y-%m-%dT%H:%M:%S.%f',
    '%Y-%m-%dT%H:%M',
    '%Y-%m-%d %H:%M:%S',
    '%Y-%m-%d %H:%M:%S.%f',
    '%Y-%m-%d %H:%M',
    '%Y-%m-%d'
]
