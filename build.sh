#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="dinet-kouxing-api"
VERSION=${1:-"latest"}
REGISTRY=${REGISTRY:-"your-registry.com"}

echo -e "${GREEN}🚀 开始构建 ${PROJECT_NAME}:${VERSION}${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请启动Docker${NC}"
    exit 1
fi

# 清理旧的构建缓存
echo -e "${YELLOW}🧹 清理构建缓存...${NC}"
docker builder prune -f

# 构建开发版本
echo -e "${YELLOW}🔨 构建开发版本...${NC}"
docker build -t ${PROJECT_NAME}:${VERSION}-dev .

# 构建生产版本
echo -e "${YELLOW}🔨 构建生产版本...${NC}"
docker build -f Dockerfile.prod -t ${PROJECT_NAME}:${VERSION} .

# 标记镜像
if [ "$REGISTRY" != "your-registry.com" ]; then
    echo -e "${YELLOW}🏷️  标记镜像...${NC}"
    docker tag ${PROJECT_NAME}:${VERSION}-dev ${REGISTRY}/${PROJECT_NAME}:${VERSION}-dev
    docker tag ${PROJECT_NAME}:${VERSION} ${REGISTRY}/${PROJECT_NAME}:${VERSION}
    docker tag ${PROJECT_NAME}:${VERSION} ${REGISTRY}/${PROJECT_NAME}:latest
fi

# 显示构建的镜像
echo -e "${GREEN}✅ 构建完成！${NC}"
echo -e "${YELLOW}📦 构建的镜像：${NC}"
docker images | grep ${PROJECT_NAME}

# 询问是否推送到仓库
if [ "$REGISTRY" != "your-registry.com" ]; then
    read -p "是否推送到镜像仓库? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}📤 推送镜像到仓库...${NC}"
        docker push ${REGISTRY}/${PROJECT_NAME}:${VERSION}-dev
        docker push ${REGISTRY}/${PROJECT_NAME}:${VERSION}
        docker push ${REGISTRY}/${PROJECT_NAME}:latest
        echo -e "${GREEN}✅ 推送完成！${NC}"
    fi
fi

echo -e "${GREEN}🎉 所有操作完成！${NC}"