# Generated by Django 3.2 on 2025-08-16 13:11

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('filestorage', '0002_alter_ossfilestorage_id'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='ossfilestorage',
            options={'ordering': ['-created_at'], 'verbose_name': '文件存储', 'verbose_name_plural': '文件存储'},
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='创建时间'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='description',
            field=models.TextField(blank=True, default='', verbose_name='描述'),
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='media_type',
            field=models.CharField(choices=[('video', '视频'), ('audio', '音频')], default='video', max_length=10, verbose_name='素材类型'),
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='original_name',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='原始文件名'),
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='user_id',
            field=models.IntegerField(default=0, verbose_name='用户ID'),
        ),
        migrations.AddField(
            model_name='ossfilestorage',
            name='username',
            field=models.CharField(blank=True, default='', max_length=150, verbose_name='用户名'),
        ),
        migrations.AddIndex(
            model_name='ossfilestorage',
            index=models.Index(fields=['user_id', 'media_type'], name='oss_file_st_user_id_a55407_idx'),
        ),
        migrations.AddIndex(
            model_name='ossfilestorage',
            index=models.Index(fields=['created_at'], name='oss_file_st_created_74a862_idx'),
        ),
        migrations.AlterModelTable(
            name='ossfilestorage',
            table='oss_file_storage',
        ),
    ]
