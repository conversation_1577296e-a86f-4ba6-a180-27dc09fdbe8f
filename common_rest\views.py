# coding=utf-8


import logging
import sys

from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.utils.module_loading import import_string

from pymodm import errors as mongo_errors

from rest_framework import generics
from rest_framework import status, exceptions
from rest_framework.response import Response
from rest_framework.views import set_rollback
from rest_framework.request import Request

from utils_common.extra_params_processor import process_extra

from .exceptions import ParameterTypeError, ParameterError, ParameterForSerializerError
from .permissions import CUSTOM_PERMISSION
from .pagination import ClientControledPageNumberPagination

logger = logging.getLogger('common.logger')  # pylint: disable=C0103
_err_logger = logging.getLogger('rest.logger')  # pylint: disable=C0103


def get_union_fields(lookup_union_fields, data):
    for union_fields in lookup_union_fields:
        union_params = {}
        for field in union_fields:
            if field not in data:
                union_params = None
                break
            union_params[field] = data[field]
        if union_params is not None:
            return union_params
    return None


def expose(allowed_methods):
    def decorator(func):
        func.exposed = True
        func.allowed_methods = [m.upper() for m in allowed_methods]
        return func

    return decorator


def exception_handler(exc, context):  # pylint: disable=unused-argument
    headers = {}
    if getattr(exc, 'auth_header', None):
        headers['WWW-Authenticate'] = exc.auth_header
    if getattr(exc, 'wait', None):
        headers['Retry-After'] = '%d' % exc.wait

    if hasattr(exc, 'detail'):
        if isinstance(exc.detail, (list, dict)):
            data = exc.detail
        else:
            data = {'detail': exc.detail}
    else:
        data = {}
    data['error_reason'] = "Unknown server error: %s" % exc

    set_rollback()
    return Response(data, status=status.HTTP_500_INTERNAL_SERVER_ERROR, headers=headers)


class CompatibleRequest(Request):  # pylint: disable=W0223
    def _load_data_and_files(self):
        super(CompatibleRequest, self)._load_data_and_files()
        self._request._data, self._request._files = self._data, self._files


class CustomizedAPIView(generics.GenericAPIView):
    api_middleware_classes = getattr(settings, 'API_MIDDLEWARE_CLASSES', ())
    __exposed_actions = None
    lookup_union_fields = ()
    pagination_class = ClientControledPageNumberPagination

    def __init__(self, *args, **kwargs):
        self._extras = None
        self._api_middlewares = None
        self.args = None
        self.kwargs = None
        self.request = None
        self.url_name = None
        self.action_name = None
        self.headers = None
        self.response = None
        self.custom_permission = None
        super(CustomizedAPIView, self).__init__(*args, **kwargs)

    @classmethod
    def get_actions(cls):
        if cls.__exposed_actions is None:
            cls.__exposed_actions = {}
            for _var in [i for i in dir(cls) if not i.startswith('_')]:
                attr = getattr(cls, _var)
                if getattr(attr, 'exposed', False):
                    cls.__exposed_actions[attr.__name__] = [method.upper() for method in attr.allowed_methods]
        return cls.__exposed_actions

    @classmethod
    def get_view_allowed_methods(cls):
        return [m.upper() for m in cls.http_method_names if hasattr(cls, m)]

    @classmethod
    def get_action_allowed_method(cls, action_name):
        return cls.get_actions().get(action_name, None)

    def _allowed_methods(self):
        action_name = getattr(self, 'action_name', None)
        if action_name is not None:
            ret = self.get_action_allowed_method(action_name)
            if ret is None:
                return []
            return ret
        else:
            return self.get_view_allowed_methods()

    def initialize_request(self, request, *args, **kwargs):
        """
        Returns the initial request object.
        """
        parser_context = self.get_parser_context(request)

        return CompatibleRequest(
            request,
            parsers=self.get_parsers(),
            authenticators=self.get_authenticators(),
            negotiator=self.get_content_negotiator(),
            parser_context=parser_context
        )

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        # request.body
        self.args = args
        self.kwargs = kwargs
        request = self.initialize_request(request, *args, **kwargs)
        self.request = request
        self.url_name = request.resolver_match.url_name
        self.action_name = request.resolver_match[2].get('action', None)
        self.headers = self.default_response_headers  # deprecate?

        try:
            response = self.initial(request, *args, **kwargs)
            if response is None:
                response = self.get_and_invoke_handler(request)
        except KeyError as exc:
            response = Response({'error_code': 999, 'error_reason': 'Key Not Found : %s' % str(exc)}, status.HTTP_400_BAD_REQUEST)
            self._logging_4xx_request(request, response)
        except (ObjectDoesNotExist, mongo_errors.DoesNotExist) as exc:
            response = Response({'error_code': 998, 'error_reason': 'Object Does Not Exist %s' % str(exc)}, status.HTTP_404_NOT_FOUND)
            self._logging_4xx_request(request, response)
        except (MultipleObjectsReturned, mongo_errors.MultipleObjectsReturned) as exc:
            response = Response({'error_code': 997, 'error_reason': 'Multiple Objects Returned %s ' % str(exc)}, status.HTTP_400_BAD_REQUEST)
            self._logging_4xx_request(request, response)
        except ParameterTypeError as exc:
            response = Response({'error_code': 992, 'error_reason': str(exc)}, status.HTTP_400_BAD_REQUEST)
            self._logging_4xx_request(request, response)
        except ParameterError as exc:
            response = Response({'error_code': 993, 'error_reason': str(exc)}, status.HTTP_400_BAD_REQUEST)
            self._logging_4xx_request(request, response)
        except ParameterForSerializerError as exc:
            response = Response({'error_code': 993, 'error_reason': exc.error_data}, status.HTTP_400_BAD_REQUEST)
            self._logging_4xx_request(request, response)
        except exceptions.NotAuthenticated as exc:
            response = Response({'error_code': 995, 'error_reason': 'NotAuthenticated %s' % exc},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except exceptions.PermissionDenied as exc:
            response = Response({'error_code': 994,
                                 'error_reason': 'PermissionDenied %s' % exc},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except exceptions.AuthenticationFailed as exc:
            response = Response({'error_code': 990,
                                 'error_reason': 'AuthenticationFailed %s' % exc},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except exceptions.MethodNotAllowed as exc:
            response = Response({'error_code': 996,
                                 'error_reason': 'MethodNotAllowed %s' % exc},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except exceptions.NotFound as exc:
            response = Response({'error_code': 991,
                                 'error_reason': 'NotFound %s' % exc},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except exceptions.Throttled as exc:
            response = Response({'error_code': 989,
                                 'error_reason': 'Throttled limited %s' % exc,
                                 'wait': exc.wait if hasattr(exc, 'wait') else 24 * 3600},
                                exc.status_code)
            self._logging_4xx_request(request, response)
        except Exception as exc:  # pylint: disable=broad-except
            extra = {'request': request._request}  # pylint: disable=W0212
            extra['query_params'] = request.query_params
            if request.method.upper() != 'GET':
                extra['request_data'] = request.data
            _err_logger.error('Server Error %s', exc, exc_info=True, extra=extra)
            response = self.handle_exception(exc)
            response.data['error_code'] = 911
        self.response = self.finalize_response(request, response, *args, **kwargs)
        return self.response

    def _logging_4xx_request(self, request, response):
        extra = {'request': request._request}  # pylint: disable=W0212
        if request.query_params:
            extra['query_params'] = request.query_params
        if request.method.upper() != 'GET' and request.data:
            extra['request_data'] = request.data
        if '__debug' in request.query_params:
            logger.error("debug request %s: %s", request.query_params['__debug'], response.data, exc_info=True, extra=extra)
            return
        else:
            exc_type = sys.exc_info()[0]
            infos = [
                request.path.replace('%', '%%'),
                "code: %s,%s" % (response.status_code, response.data.get("error_code", "NOCODE")),
                "exc: %s" % exc_type.__name__ if exc_type is not None else 'NOEXCINFO',
                "%s",
            ]
            event = ";".join(infos)
            logger.error(event, response.data.get("error_reason", "NOREASON"), extra=extra)

    def initial(self, request, *args, **kwargs):
        for _middleware in self.api_middlewares:
            ret = _middleware.preprocess_request(request, self)
            if ret is not None:
                return ret
        return super(CustomizedAPIView, self).initial(request, *args, **kwargs)

    def finalize_response(self, request, response, *args, **kwargs):
        for _middleware in reversed(self.api_middlewares):
            ret = _middleware.process_response(request, response, self)
            if ret is not None:
                response = ret
                break
        return super(CustomizedAPIView, self).finalize_response(request, response, *args, **kwargs)

    def get_and_invoke_handler(self, request, *args, **kwargs):
        # Get the appropriate handler method
        handler = self.get_handler(request)
        response = handler(request, *args, **kwargs)
        if response is None:  # parameter error
            raise ParameterError("Error parameter")
        return response

    def _get_action(self, method, action_name):
        handler = getattr(self, action_name, None)
        if handler is None or not getattr(handler, 'exposed', False):
            raise exceptions.NotFound(' '.join([method, action_name]))
        if method.upper() not in getattr(handler, 'allowed_methods', ()):
            raise exceptions.MethodNotAllowed(' '.join([method, action_name]))
        return handler

    def get_handler(self, request):
        if request.method.upper() == "OPTIONS":
            return self.options

        if self.action_name:
            handler = self._get_action(request.method.lower(), self.action_name)
        elif request.method.lower() in self.http_method_names:
            handler = getattr(self, request.method.lower(),
                              self.http_method_not_allowed)
        else:
            handler = self.http_method_not_allowed
        return handler

    @property
    def api_middlewares(self):
        if self._api_middlewares is None:
            self._api_middlewares = []
            for _ in self.api_middleware_classes:
                cls = import_string(_)
                ins = cls()
                self._api_middlewares.append(ins)
        return self._api_middlewares

    def check_permissions(self, request):
        url_name = self.request.resolver_match.url_name
        method_name = request.method.upper()
        custom_permission_class = CUSTOM_PERMISSION.get_custom_permission_class(url_name, method_name, self.action_name)
        if custom_permission_class is not None:
            self.custom_permission = custom_permission_class()
            res = self.custom_permission.has_permission(request, self)
            if res:
                return True
            else:
                self.permission_denied(
                    request, message=getattr(self.custom_permission, 'message', None)
                )
        permissions = self.get_permissions()
        if permissions:
            self.custom_permission = permissions[0]
        # only staff users has permission to access
        generics.GenericAPIView.check_permissions(self, request)

    def options(self, request, *args, **kwargs):
        if self.request.query_params.get('simple_permission_description', None):
            return super(CustomizedAPIView, self).options(request, *args, **kwargs)
        ret_dict = {}
        if 'permissions' in self.get_extra():
            if getattr(self, 'custom_permission', None) is not None:
                desc_method = getattr(self.custom_permission, 'get_permission_description', None)
                if desc_method is not None:
                    ret_dict['permissions'] = desc_method(self.request, self)  # pylint: disable=E1102
        ret = Response(ret_dict)
        ret['Allow'] = ', '.join(self._allowed_methods())
        return ret
        # return generics.GenericAPIView.options(self, request, *args, **kwargs)

    def get_queryset(self):
        if self.queryset is None:
            queryset = self.model.objects.all()
        else:
            queryset = self.queryset.all()
        return queryset

    def get_lookup_params(self):
        if self.lookup_field in self.request.query_params:
            return {self.lookup_field: self.request.query_params[self.lookup_field]}
        if self.request.method.upper() != 'GET' and self.lookup_field in self.request.data:
            return {self.lookup_field: self.request.data[self.lookup_field]}
        union_params = get_union_fields(self.lookup_union_fields, self.request.query_params)
        if union_params is not None:
            return union_params
        if self.request.method.upper() != 'GET':
            union_params = get_union_fields(self.lookup_union_fields, self.request.data)
            if union_params is not None:
                return union_params
        return None

    def get_object(self):
        queryset = self.filter_queryset(self.get_queryset())
        lookup_params = self.get_lookup_params()
        if lookup_params is None:
            raise exceptions.NotFound("Can't find object with get_object params")
        else:
            return queryset.get(**lookup_params)

    def get_serializer(self, *args, **kwargs):
        serializer_class = kwargs.pop('serializer_class', self.get_serializer_class())
        kwargs['context'] = self.get_serializer_context()
        extras = self.get_extra()
        if isinstance(extras, dict) and extras:
            kwargs['extras'] = extras
        return serializer_class(*args, **kwargs)

    def get_extra(self):
        if self._extras is None:
            self._extras = process_extra([item for item in self.request.query_params.get('extras', '').split(',') if item])
        return self._extras

    def get_list_items(self, queryset, **kwargs):
        data = []
        for query in queryset:
            data.append(self.get_item(query, **kwargs))
        return data

    def get_item(self, obj, **kwargs):
        return self.get_serializer(instance=obj, **kwargs).data

    def put_item_and_return(self, obj, data=None):
        if data is None:
            data = self.request.data
        serializer = self.get_serializer(instance=obj, data=data, partial=True)
        return self.save_serializer_and_return(serializer)

    def delete_item(self, obj):
        data = self.get_item(obj)
        obj.delete()
        return data

    def save_serializer_and_return(self, serializer):
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status.HTTP_200_OK)
        else:
            return self.serializer_error_response(serializer)

    def serializer_error_response(self, serializer):
        raise ParameterForSerializerError(serializer.errors)

    def _should_paginated(self):
        if self.paginator is None:
            return False
        if isinstance(self.paginator, ClientControledPageNumberPagination) and 'page_size' in self.request.query_params:
            return True

    def _get_paginated(self):
        return self.get_paginated_response(self.get_list_items(self.paginate_queryset(self.filter_queryset(self.get_queryset()))))

    def _i_list(self, request):  # pylint: disable=unused-argument
        if self._should_paginated():
            return self._get_paginated()
        else:
            return Response(self.get_list_items(self.filter_queryset(self.get_queryset())))

    def _i_get(self, request):
        if 'many' in request.query_params:
            return self._i_list(request)
        return Response(self.get_item(self.get_object()))

    def _i_post(self, request):
        serializer = self.get_serializer(data=request.data)
        return self.save_serializer_and_return(serializer)

    def _i_post_and_put(self, request):
        try:
            obj = self.get_object()
            return self._i_put(request, instance=obj)
        except ObjectDoesNotExist:
            return self._i_post(request)

    def _i_put(self, request, instance=None):  # pylint: disable=unused-argument
        if instance is None:
            instance = self.get_object()
        return self.put_item_and_return(instance)

    def _i_delete(self, request):  # pylint: disable=unused-argument
        return Response(self.delete_item(self.get_object()))


class ListByIdsView(CustomizedAPIView):
    """
        允许用户通过id或者ids来访问对象
        注意: 不设对象级别的权限控制, 仅在用户有权限访问该model所有对象的时候才可以使用
    """

    def get(self, request):
        if 'id' in request.query_params:
            _id = request.query_params['id']
            return Response(self.get_item(self.model.objects.get(pk=_id)))
        elif 'ids' in request.query_params:
            return Response(self.get_list_items(self.get_queryset()))
        else:
            return self._i_get(request)

    def get_queryset(self):
        if self.queryset:
            return self.queryset.all()
        if 'ids' in self.request.query_params:
            if self.request.query_params['ids'] == '':
                raise ParameterError('get "ids" in request.query_params with illegal value')
            instances = list(self.model.objects.filter(pk__in=self.request.query_params['ids'].split(',')))
            instances.sort(key=lambda t: self.request.query_params['ids'].split(',').index(str(t.pk)))
            return instances
        return super(ListByIdsView, self).get_queryset()
