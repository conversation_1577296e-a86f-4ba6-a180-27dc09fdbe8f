# DINet 模型配置文件

model:
  name: "dinet"
  version: "v1.0"
  
  # 模型参数
  input_size: 256
  num_frames: 16
  latent_dim: 512
  
  # 推理参数
  batch_size: 1
  device: "cuda"  # cuda 或 cpu
  dtype: "float16"  # float16 或 float32

# 数据处理配置
data:
  video_fps: 25
  audio_sample_rate: 16000
  resolution: 256
  num_frames: 16
  
  # 预处理参数
  face_detection: true
  face_alignment: true
  mouth_crop: true

# 输出配置
output:
  format: "mp4"
  quality: "high"  # high, medium, low
  fps: 25
  
# 缓存配置
cache:
  enabled: true
  ttl: 86400  # 24小时
  max_size: "10GB"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"