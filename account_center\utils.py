import base64
from gmssl import sm2
from django.conf import settings
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from rest_framework import exceptions


# sm2 加密
def sm2_encrypt(info):
    sm2_crypt = sm2.CryptSM2(public_key=settings.SM2_PUBLIC_KEY, private_key='')

    encode_info = sm2_crypt.encrypt(info.encode(encoding="utf-8"))
    encode_info = base64.b64encode(encode_info).decode()  # 将二进制bytes通过base64编码
    return encode_info


# rsa 加密
def rsa_encrypt(info):
    pubkey = settings.SM2_PUBLIC_KEY
    pubkey = '-----BEGIN RSA PUBLIC KEY-----\n' + pubkey + '\n-----END RSA PUBLIC KEY-----'

    rsakey = RSA.importKey(pubkey)
    cipher_rsa = PKCS1_v1_5.new(rsakey)
    cipher_text = base64.b64encode(cipher_rsa.encrypt(info.encode('utf-8'))).decode()
    return cipher_text
