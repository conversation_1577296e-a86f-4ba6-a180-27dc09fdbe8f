# -*- coding: utf-8 -*-

import os
import base64
import string
import random
import binascii
import traceback
from Cryptodome.PublicKey import RSA
from Cryptodome.Cipher import PKCS1_v1_5

from django.utils import timezone
from django.dispatch import Signal

from .models import AutoExpiredAuthToken, RsaKeyPair
from .serializers import AutoExpiredAuthTokenSerializer

user_key_changed_signal = Signal(providing_args=['user_id', 'key'])


class RestAuthHmyUtils(object):
    @staticmethod
    def get_valid_auth_token(user, delta=None, refresh=False):
        auth_token = AutoExpiredAuthToken.objects.filter(user=user).first()
        updated = False
        if auth_token is None:
            auth_token = AutoExpiredAuthToken(user=user)
            auth_token.key = RestAuthHmyUtils.generate_key()
            updated = True
        if refresh or auth_token.expire_in < 3600 * 24:  # refresh key with each login request!!!
            # if refresh and auth_token.expire_in < 3600 * 24:
            auth_token.key = RestAuthHmyUtils.generate_key()
            if delta is None:
                delta = timezone.timedelta(days=30)
            auth_token.expire_time = timezone.now() + delta
            updated = True
        if updated:
            auth_token.save()
            user_key_changed_signal.send(sender='rest_auth_common', user_id=auth_token.user_id, key=auth_token.key)
        return auth_token

    @staticmethod
    def login_and_get_response_data(user, delta=None, refresh=True):
        return AutoExpiredAuthTokenSerializer(RestAuthHmyUtils.get_valid_auth_token(user, delta, refresh)).data

    @staticmethod
    def generate_key():
        return binascii.hexlify(os.urandom(20)).decode("ascii")


def gen_pwd(length=16):
    chars = string.ascii_letters + string.digits + '!@#$%^&*()'
    random.seed = (os.urandom(1024))
    return ''.join(random.choice(chars) for i in range(length))


def decrypt_pwd(rsa_key_id, password):
    try:
        rkp_obj = RsaKeyPair.objects.get(id=rsa_key_id)
        private_key = RSA.importKey(rkp_obj.private_key)
        rsa_object = PKCS1_v1_5.new(private_key)
        new_password = base64.b64decode(password)
        new_password = rsa_object.decrypt(new_password, '')
        return new_password
    except RsaKeyPair.DoesNotExist:
        # print(traceback.format_exc())
        # TODO: logger
        pass
    except:
        # print(traceback.format_exc())
        # TODO: logger
        pass

    return password


def get_cache_key(captcha_key):
    return "captcha_key_%s" % captcha_key


def random_char_challenge(length):
    chars = 'abcdefghijklmnopqrstuvwxyz'
    ret = ''
    for i in range(length):
        ret += random.choice(chars)
    return ret.upper()


def filter_smooth(image, filter_code):
    return image.filter(filter_code)


def noise_dots(draw, image, fill):
    size = image.size
    for p in range(int(size[0] * size[1] * 0.1)):
        x = random.randint(0, size[0])
        y = random.randint(0, size[1])
        draw.point((x, y), fill=fill)
    return draw


def noise_arcs(draw, image, fill):
    size = image.size
    draw.arc([-20, -20, size[0], 20], 0, 295, fill=fill)
    draw.line([-20, 20, size[0] + 20, size[1] - 20], fill=fill)
    draw.line([-20, 0, size[0] + 20, size[1]], fill=fill)
    return draw
