#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Django Cache Integration for DINet

This script tests the Django cache functionality in the DINet caching system.
"""

import os
import sys
import time
import django
from pathlib import Path

# Setup Django environment
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ninth_hosp_vein_api.settings')

try:
    django.setup()
    print("✅ Django initialized successfully")
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"❌ Django initialization failed: {e}")
    DJANGO_AVAILABLE = False

def test_django_cache_basic():
    """Test basic Django cache functionality"""
    print("\n🧪 Testing Django Cache Basic Functionality")
    print("-" * 45)

    if not DJANGO_AVAILABLE:
        print("❌ Django not available, skipping test")
        return False

    try:
        from django.core.cache import cache

        # Test basic cache operations
        test_key = "dinet_test_key"
        test_value = {"test": "data", "timestamp": 1234567890}

        # Set cache
        cache.set(test_key, test_value, 60)  # 60 seconds TTL
        print(f"✅ Cache set: {test_key}")

        # Get cache
        cached_value = cache.get(test_key)
        if cached_value == test_value:
            print(f"✅ Cache get successful: {cached_value}")
        else:
            print(f"❌ Cache get failed: expected {test_value}, got {cached_value}")
            return False

        # Delete cache
        cache.delete(test_key)
        deleted_value = cache.get(test_key)
        if deleted_value is None:
            print("✅ Cache delete successful")
        else:
            print(f"❌ Cache delete failed: value still exists: {deleted_value}")
            return False

        return True

    except Exception as e:
        print(f"❌ Django cache test failed: {e}")
        return False

def test_dinet_cache_manager():
    """Test DINet cache manager with Django cache"""
    print("\n🧪 Testing DINet Cache Manager with Django")
    print("-" * 45)

    if not DJANGO_AVAILABLE:
        print("❌ Django not available, skipping test")
        return False

    try:
        from dinet_cache_strategy import DINetCacheManager

        # Initialize cache manager
        cache_manager = DINetCacheManager(
            cache_dir="./test_cache",
            enable_django_cache=True,
            cache_timeout=300,  # 5 minutes for testing
            max_memory_cache_size=10
        )
        print("✅ DINet cache manager initialized")

        # Test cache statistics
        stats = cache_manager.get_cache_stats()
        print(f"✅ Cache stats retrieved: {len(stats)} metrics")
        for key, value in stats.items():
            print(f"   {key}: {value}")

        # Test cache clearing
        print("\n🗑️  Testing cache clear...")
        cache_manager.clear_cache("django")
        print("✅ Django cache clear completed")

        return True

    except Exception as e:
        print(f"❌ DINet cache manager test failed: {e}")
        return False

def test_cache_performance():
    """Test cache performance comparison"""
    print("\n⚡ Testing Cache Performance")
    print("-" * 30)

    if not DJANGO_AVAILABLE:
        print("❌ Django not available, skipping test")
        return False

    try:
        import time
        from django.core.cache import cache

        # Test data
        test_data = {
            'large_array': list(range(1000)),
            'metadata': {
                'size': 1000,
                'type': 'test',
                'timestamp': time.time()
            }
        }

        # Test set performance
        start_time = time.time()
        for i in range(10):
            cache.set(f"perf_test_{i}", test_data, 60)
        set_time = time.time() - start_time
        print(f"✅ Set 10 items in {set_time:.4f}s ({set_time/10:.4f}s per item)")

        # Test get performance
        start_time = time.time()
        retrieved_items = []
        for i in range(10):
            item = cache.get(f"perf_test_{i}")
            retrieved_items.append(item)
        get_time = time.time() - start_time
        print(f"✅ Get 10 items in {get_time:.4f}s ({get_time/10:.4f}s per item)")

        # Verify data integrity
        all_correct = all(item == test_data for item in retrieved_items if item is not None)
        print(f"✅ Data integrity: {'Passed' if all_correct else 'Failed'}")

        # Clean up
        for i in range(10):
            cache.delete(f"perf_test_{i}")
        print("✅ Performance test cleanup completed")

        return True

    except Exception as e:
        print(f"❌ Cache performance test failed: {e}")
        return False

def test_cache_integration():
    """Test integration with Django cache backends"""
    print("\n🔗 Testing Cache Backend Integration")
    print("-" * 35)

    if not DJANGO_AVAILABLE:
        print("❌ Django not available, skipping test")
        return False

    try:
        from django.core.cache import cache
        from django.conf import settings

        # Get cache backend information
        cache_config = getattr(settings, 'CACHES', {})
        default_cache = cache_config.get('default', {})
        backend = default_cache.get('BACKEND', 'Unknown')

        print(f"📦 Cache backend: {backend}")

        if 'redis' in backend.lower():
            print("🔴 Redis backend detected")
            location = default_cache.get('LOCATION', 'Not specified')
            print(f"   Location: {location}")
        elif 'memcached' in backend.lower():
            print("🟢 Memcached backend detected")
        elif 'locmem' in backend.lower():
            print("💾 Local memory backend detected")
        elif 'filebased' in backend.lower():
            print("📁 File-based backend detected")
        else:
            print(f"❓ Other backend: {backend}")

        # Test backend-specific features
        try:
            # Test TTL functionality
            cache.set("ttl_test", "test_value", 1)  # 1 second TTL
            immediate_get = cache.get("ttl_test")
            print(f"✅ TTL test - immediate get: {immediate_get}")

            time.sleep(2)  # Wait for expiration
            expired_get = cache.get("ttl_test")
            if expired_get is None:
                print("✅ TTL expiration working correctly")
            else:
                print("⚠️  TTL might not be working as expected")

        except Exception as e:
            print(f"⚠️  TTL test failed: {e}")

        return True

    except Exception as e:
        print(f"❌ Cache integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Django Cache Integration Test Suite")
    print("=" * 45)

    results = []

    # Run tests
    results.append(test_django_cache_basic())
    results.append(test_dinet_cache_manager())
    results.append(test_cache_performance())
    results.append(test_cache_integration())

    # Summary
    print(f"\n📋 Test Summary")
    print("-" * 15)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")

    if passed == total:
        print("✅ All Django cache tests passed!")
        print("\n🎯 Django cache integration is ready for production")
        print("\n📝 Configuration recommendations:")
        print("1. Use Redis backend for production: CACHES['default']['BACKEND'] = 'django_redis.cache.RedisCache'")
        print("2. Set appropriate cache timeouts: default 24 hours for DINet features")
        print("3. Monitor cache hit rates and performance")
        print("4. Set up cache key versioning for model updates")
    else:
        print("❌ Some tests failed. Check configuration and dependencies.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure Django settings are properly configured")
        print("2. Check cache backend availability (Redis/Memcached)")
        print("3. Verify cache permissions and storage")

    return passed == total

if __name__ == "__main__":
    success = main()

    print(f"\n{'='*45}")
    print("Cache backend comparison:")
    print("- Redis: Best performance, persistence, clustering")
    print("- Memcached: Good performance, simple setup")
    print("- Local Memory: Development only, no persistence")
    print("- File-based: Fallback option, slower performance")

    sys.exit(0 if success else 1)
