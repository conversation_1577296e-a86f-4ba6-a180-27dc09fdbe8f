from django.contrib.auth.models import User as DJUser
from django.contrib.auth.models import AnonymousUser
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from rest_auth_common.models import AutoExpiredAuthToken, UserEnterpriseAppIdSecret
from common_rest.api_middlewares import BaseApiMiddleware
from rest_framework.response import Response
import datetime
import pytz
import json
import time
import hmac
import copy

timezone_eight = pytz.timezone('Asia/Shanghai')

import logging
logger = logging.getLogger('common.logger')

def gen_session_id(user_id):
    token = AutoExpiredAuthToken.objects.get(user_id=user_id)
    return '{}_{}'.format(token.user.username, token.key)


class AddSessionIdMiddleware(MiddlewareMixin):
    def __init__(self, get_response=None):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_request(self, request):
        pass

    def process_template_response(self, request, response):
        request_path = request.get_full_path()
        if request_path in [
            '/auth/auth/rsa_login/',
            ]:
            if "user_id" in response.data:
                user = DJUser.objects.get(id=(response.data["user_id"]))
                account = getattr(user, 'account', None)
        try:
            if "user_id" in response.data:
                response.data["session_id"] = gen_session_id(response.data["user_id"])
        except:
            pass
        return response

class HTTPREFERERMiddleware(MiddlewareMixin):

    def preprocess_request(self, request, view):
        if not settings.ALLOWED_REFERER_LIST:
            return None
        referer = request.META.get('HTTP_REFERER', None)
        logger.info(referer)
        logger.info(settings.ALLOWED_REFERER_LIST)
        if referer is None:
            return Response({"error_code": 1, "error_reason": "非法的请求来源"})

        for allowed_referer in settings.ALLOWED_REFERER_LIST:
            if allowed_referer in referer:
                break
        else:
            return Response({"error_code": 1, "error_reason": "非法的请求来源"})
        return None

    def process_response(self, request, response, views):
        pass
