'''
Created on Aug 2, 2014

@author: xiongxt
'''
from django.contrib.auth.models import User
from rest_framework import authentication

from django.conf import settings


class SessionAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        user = getattr(request._request, 'user', None)
        if user is not None and user.is_authenticated:
            return user, None


class DebugDevAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        if settings.DEBUG:
            if '__user_id' in request.query_params:
                return User.objects.get(pk=request.query_params['__user_id']), None
            if '__user_id' in request.data:
                return User.objects.get(pk=request.data['__user_id']), None
