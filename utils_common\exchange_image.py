# -*- coding: utf-8 -*-


import base64
import re

prefix_regex = re.compile(r'data:image\/[A-Za-z0-9\.]*;base64,')


def check_prefix(image__base64):
    ret = re.match(prefix_regex, image__base64)
    if ret:
        prefix = ret.group(0)
        image_type = prefix[11:][:-8]
        real_image__base64 = image__base64[len(prefix):]
        return image_type, real_image__base64
    return ret, image__base64


def get_image_bytes_from_base64(image__base64):
    image_type, image__base64 = check_prefix(image__base64)
    img_bytes = base64.b64decode(image__base64)
    return image_type, img_bytes
