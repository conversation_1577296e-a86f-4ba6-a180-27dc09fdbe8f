# coding=utf-8

from rest_framework import fields


def bind(self, field_name, parent):
    self.field_name = field_name
    self.parent = parent

    # `self.label` should default to being based on the field name.
    if self.label is None:
        self.label = field_name.replace('_', ' ').capitalize()

    # self.source should default to being the same as the field name.
    if self.source is None:
        self.source = field_name

    # self.source_attrs is a list of attributes that need to be looked up
    # when serializing the instance, or populating the validated data.
    if self.source == '*':
        self.source_attrs = []
    else:
        self.source_attrs = self.source.split('.')


fields.Field.bind = bind


def method_bind(self, field_name, parent):
    # In order to enforce a consistent style, we error if a redundant
    # 'method_name' argument has been used. For example:
    # my_field = serializer.SerializerMethodField(method_name='get_my_field')
    default_method_name = 'get_{field_name}'.format(field_name=field_name)

    # The method name should default to `get_{field_name}`.
    if self.method_name is None:
        self.method_name = default_method_name

    super(fields.SerializerMethodField, self).bind(field_name, parent)


fields.SerializerMethodField.bind = method_bind
