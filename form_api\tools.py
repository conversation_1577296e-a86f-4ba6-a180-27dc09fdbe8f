
import os
import gc

# Try to import ML libraries, gracefully handle missing dependencies
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    print("⚠️  PyTorch not available - inference will not work")
    TORCH_AVAILABLE = False
    torch = None

try:
    from omegaconf import OmegaConf
    OMEGACONF_AVAILABLE = True
except ImportError:
    print("⚠️  OmegaConf not available - using simple config")
    OMEGACONF_AVAILABLE = False
    OmegaConf = None

try:
    from accelerate.utils import set_seed
    ACCELERATE_AVAILABLE = True
except ImportError:
    print("⚠️  Accelerate not available - using manual seed")
    ACCELERATE_AVAILABLE = False
    set_seed = None

# Try to import DINet components
try:
    from latentsync.models.DINet import DINet
    from latentsync.pipelines.dinet_pipeline import DINetPipeline
    from latentsync.whisper.audio2feature import Audio2Feature
    DINET_AVAILABLE = True
except ImportError:
    print("⚠️  DINet components not available")
    DINET_AVAILABLE = False
    DINet = None
    DINetPipeline = None
    Audio2Feature = None

# Import cache manager
import sys
import os
cache_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if cache_dir not in sys.path:
    sys.path.append(cache_dir)

try:
    from dinet_cache_strategy import DINetCacheManager
    CACHE_AVAILABLE = True
    print("🚀 Full cache system loaded")
except ImportError:
    try:
        from simple_cache import DINetCacheManager
        CACHE_AVAILABLE = True
        print("📦 Simple cache system loaded")
    except ImportError as e:
        print(f"⚠️  No cache system available: {e}")
        DINetCacheManager = None
        CACHE_AVAILABLE = False


def check_dependencies():
    """Check if required dependencies are available"""
    missing = []
    if not TORCH_AVAILABLE:
        missing.append("torch")
    if not DINET_AVAILABLE:
        missing.append("latentsync (DINet components)")

    return len(missing) == 0, missing


def inference(config, video_path, audio_path, video_out_path, inference_ckpt_path, seed=1247, enable_cache=True):
    """
    DINet inference with caching support

    Args:
        config: Configuration object
        video_path: Path to input video
        audio_path: Path to input audio
        video_out_path: Path for output video
        inference_ckpt_path: Path to model checkpoint
        seed: Random seed for reproducibility
        enable_cache: Enable/disable caching functionality
    """

    # Check dependencies first
    deps_ok, missing_deps = check_dependencies()
    if not deps_ok:
        error_msg = f"Missing required dependencies: {', '.join(missing_deps)}"
        print(f"❌ {error_msg}")
        raise ImportError(error_msg)

    # Initialize cache manager
    cache_manager = None
    if enable_cache and CACHE_AVAILABLE and DINetCacheManager:
        try:
            cache_manager = DINetCacheManager(
                cache_dir="./dinet_cache",
                enable_django_cache=True,
                cache_timeout=24 * 3600,  # 24 hours
                max_memory_cache_size=50
            )
            print("🚀 Cache manager initialized")
        except Exception as e:
            print(f"⚠️  Cache initialization failed: {e}")
            cache_manager = None
    elif enable_cache:
        print("⚠️  Cache requested but not available")

    # Check if the GPU supports float16
    dtype = torch.float32 if TORCH_AVAILABLE else None
    device = "cuda:0" if TORCH_AVAILABLE and torch.cuda.is_available() else "cpu"

    try:
        if config.model.cross_attention_dim == 768:
            whisper_model_path = "checkpoints/whisper/small.pt"
        elif config.model.cross_attention_dim == 384:
            whisper_model_path = "checkpoints/whisper/tiny.pt"
        else:
            raise NotImplementedError("cross_attention_dim must be 768 or 384")

        audio_encoder = Audio2Feature(
            model_path=whisper_model_path,
            device=device,
            num_frames=config.data.num_frames
        )

        # Try to load cached audio features
        cached_audio_features = None
        if cache_manager:
            cached_audio_features = cache_manager.load_audio_features(audio_path)
            if cached_audio_features:
                print("🎯 Using cached audio features")

        dinet = DINet(3, config.data.num_face_ref * 3, config.model.cross_attention_dim)
        if os.path.exists(inference_ckpt_path):
            ckpt = torch.load(inference_ckpt_path, map_location="cpu")
            dinet.load_state_dict(ckpt['dinet'])
        else:
            raise FileNotFoundError(f"Checkpoint file not found: {inference_ckpt_path}")

        dinet.eval()
        dinet = dinet.to(device=device,dtype=dtype)

        pipeline = DINetPipeline(
            dinet,
            audio_encoder,
            device,
            dtype,
        )

        if seed != -1:
            if ACCELERATE_AVAILABLE and set_seed:
                set_seed(seed)
            elif TORCH_AVAILABLE:
                torch.manual_seed(seed)
        else:
            if TORCH_AVAILABLE:
                torch.seed()

        with torch.no_grad():
            # Create enhanced pipeline call with caching
            if cache_manager and cached_audio_features:
                # Use cached audio features in pipeline
                print("🔥 Running inference with cached audio features")
                result = pipeline(
                    video_path=video_path,
                    audio_path=audio_path,
                    video_out_path=video_out_path,
                    num_frames=config.data.num_frames,
                    width=config.data.resolution,
                    height=config.data.resolution,
                    mask=config.data.mask_image_path,
                    cache_manager=cache_manager,
                    cached_audio_features=cached_audio_features
                )
            else:
                # Standard pipeline call
                print("⚡ Running standard inference")
                result = pipeline(
                    video_path=video_path,
                    audio_path=audio_path,
                    video_out_path=video_out_path,
                    num_frames=config.data.num_frames,
                    width=config.data.resolution,
                    height=config.data.resolution,
                    mask=config.data.mask_image_path,
                    cache_manager=cache_manager
                )
    except Exception as e:
        print(f"An error occurred during inference: {e}")
        raise e
    finally:
        # Display cache statistics if available
        if cache_manager:
            try:
                stats = cache_manager.get_cache_stats()
                print("📊 Cache Statistics:")
                print(f"   Video cache: {stats.get('video_cache_count', 0)} entries")
                print(f"   Audio cache: {stats.get('audio_cache_count', 0)} entries")
                print(f"   Memory cache: {stats.get('memory_cache_count', 0)} entries")
                print(f"   Total cache size: {stats.get('total_cache_size_mb', 0):.2f} MB")
            except Exception as cache_error:
                print(f"Cache stats error: {cache_error}")

        # Cleanup
        import gc
        gc.collect()
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
        print("🧹 Memory cleanup completed")

    return video_out_path


def clear_inference_cache():
    """Utility function to clear DINet inference cache"""
    if not CACHE_AVAILABLE or not DINetCacheManager:
        print("⚠️  Cache functionality not available")
        return False

    try:
        cache_manager = DINetCacheManager()
        cache_manager.clear_cache()
        print("🗑️  Inference cache cleared successfully")
        return True
    except Exception as e:
        print(f"Cache clear error: {e}")
        return False


def get_inference_cache_stats():
    """Utility function to get cache statistics"""
    if not CACHE_AVAILABLE or not DINetCacheManager:
        print("⚠️  Cache functionality not available")
        return None

    try:
        cache_manager = DINetCacheManager()
        return cache_manager.get_cache_stats()
    except Exception as e:
        print(f"Cache stats error: {e}")
        return None

