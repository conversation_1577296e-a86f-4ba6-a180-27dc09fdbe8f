# coding=utf-8


import json
from requests import Session


class RequestException(Exception):
    def __init__(self, code):
        self.code = code

    def __str__(self):
        return "Utils error: " + self.code


class JsonApiSession(Session):
    def __init__(self):
        Session.__init__(self)
        self.headers.setdefault('content-type', 'application/json')
        self.headers.setdefault('encoding', 'utf-8')

        # self.mount('http://', HTTPAdapter(max_retries=3))

    @staticmethod
    def single_get(URL, params=None, **kwargs):
        with JsonApiSession() as s:
            return s.get(URL, params, **kwargs)

    @staticmethod
    def single_put(URL, data=None, **kwargs):
        with JsonApiSession() as s:
            return s.put(URL, data, **kwargs)

    @staticmethod
    def single_post(URL, data=None, **kwargs):
        with JsonApiSession() as s:
            return s.post(URL, data, **kwargs)

    def get(self, url, params=None, **kwargs):
        res = Session.get(self, url, params=params, **kwargs)
        if res.status_code / 100 == 2:
            res_data = json.loads(res.content)
            return res_data
        else:
            error = ("Failed to get data %s to url %s, response is %s %s!" % (params, url, res.status_code, res.content))
            raise RequestException(error)

    def put(self, url, data=None, **kwargs):
        res = Session.put(self, url, data=json.dumps(data) if data is not None else None, **kwargs)
        if res.status_code / 100 == 2:
            res_data = json.loads(res.content)
            return res_data
        else:
            error = ("Failed to put data %s to url %s, response is %s %s!" % (data, url, res.status_code, res.content))
            raise RequestException(error)

    def post(self, url, data=None, **kwargs):
        res = Session.post(self, url, data=json.dumps(data, ensure_ascii=False).encode('utf8') if data is not None else None, **kwargs)
        if res.status_code / 100 == 2:
            res_data = json.loads(res.content)
            return res_data
        else:
            error = ("Failed to post data %s to url %s, response is %s %s!" % (data, url, res.status_code, res.content))
            raise RequestException(error)
