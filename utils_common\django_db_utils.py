# -*- coding: utf-8 -*-


from functools import wraps

from django.db import connection as db_connection


def check_db_connection():
    if db_connection.connection is not None:
        if not db_connection.is_usable():
            db_connection.close()


def orm_func(func):
    @wraps(func)  # task name is wrong without it
    def wrapper(*args, **kwargs):
        check_db_connection()
        return func(*args, **kwargs)

    return wrapper
