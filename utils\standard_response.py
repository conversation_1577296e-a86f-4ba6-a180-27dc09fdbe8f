from functools import wraps
from rest_framework.response import Response


def format_response(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        if isinstance(result, Response):
            return result
        if isinstance(result, (dict, list)) and 'error_code' in result:
            return Response(result)
        return Response({'error_code': 0, 'error_reason': 'success', 'data': result})

    return wrapper
