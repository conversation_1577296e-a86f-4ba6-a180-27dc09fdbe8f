import cv2
from insightface.app import FaceAnalysis
import numpy as np
import torch

INSIGHTFACE_DETECT_SIZE = 640


class FaceDetector:
    def __init__(self, device="cuda"):
        self.app = FaceAnalysis(
            allowed_modules=["detection", "landmark_2d_106"],
            root="checkpoints/auxiliary",
            providers=[('CUDAExecutionProvider', {'device_id': cuda_to_int(device)})]
        )
        self.app.prepare(ctx_id=cuda_to_int(device), det_size=(INSIGHTFACE_DETECT_SIZE, INSIGHTFACE_DETECT_SIZE))

    def __call__(self, frame, threshold=0.5):
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        f_h, f_w, _ = frame.shape

        faces = self.app.get(frame)

        get_face_store = None
        max_size = 0

        if len(faces) == 0:
            return None, None
        else:
            for face in faces:
                bbox = face.bbox.astype(np.int_).tolist()
                w, h = bbox[2] - bbox[0], bbox[3] - bbox[1]
                if w < 50 or h < 80:
                    continue
                if w / h > 1.5 or w / h < 0.2:
                    continue
                if face.det_score < threshold:
                    continue
                size_now = w * h

                if size_now > max_size:
                    max_size = size_now
                    get_face_store = face

        if get_face_store is None:
            return None, None
        else:
            face = get_face_store
            lmk = np.round(face.landmark_2d_106).astype(np.int_)

            halk_face_coord = np.mean([lmk[74], lmk[73]], axis=0)  # lmk[73]

            sub_lmk = lmk[LMK_ADAPT_ORIGIN_ORDER]
            halk_face_dist = np.max(sub_lmk[:, 1]) - halk_face_coord[1]
            upper_bond = halk_face_coord[1] - halk_face_dist  # *0.94

            x1, y1, x2, y2 = (np.min(sub_lmk[:, 0]), int(upper_bond), np.max(sub_lmk[:, 0]), np.max(sub_lmk[:, 1]))

            if y2 - y1 <= 0 or x2 - x1 <= 0 or x1 < 0:
                x1, y1, x2, y2 = face.bbox.astype(np.int_).tolist()

            y2 += int((x2 - x1) * 0.1)
            x1 -= int((x2 - x1) * 0.05)
            x2 += int((x2 - x1) * 0.05)

            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(f_w, x2)
            y2 = min(f_h, y2)

            return (x1, y1, x2, y2), lmk


def cuda_to_int(cuda_str: str) -> int:
    """
    Convert the string with format "cuda:X" to integer X.
    """
    if cuda_str == "cuda":
        return 0
    device = torch.device(cuda_str)
    if device.type != "cuda":
        raise ValueError(f"Device type must be 'cuda', got: {device.type}")
    return device.index


LMK_ADAPT_ORIGIN_ORDER = [
    1,
    10,
    12,
    14,
    16,
    3,
    5,
    7,
    0,
    23,
    21,
    19,
    32,
    30,
    28,
    26,
    17,
    43,
    48,
    49,
    51,
    50,
    102,
    103,
    104,
    105,
    101,
    73,
    74,
    86,
]

def draw_mouth_masks( landmarks,frame):
    # the input is a point list
    # this function is used to draw a blending mask for the lower half faces
    surround_landmarks = [13,14,15,16,2,3,4,5,6,7,8,0,24,23,22,21,20,19,18,32,31,30,29,83,84,85,80,79,78,77]
    blur_kernel = int(0.1 * frame.shape[0] // 2 * 2) + 1
    # blur_kernel = 5
    points = [landmarks[idx] for idx in surround_landmarks]
    points = np.array(points)
    mask = np.ones((frame.shape[0], frame.shape[1]))
    mask = cv2.fillPoly(mask, pts=[points], color=(0, 0, 0))
    # here to learn from the muse talk applications
    # borrow from musetalk
    mask = cv2.GaussianBlur(mask, (blur_kernel, blur_kernel), 0)
    mask = 1.0 - mask
    return np.uint8(mask * 255)

if __name__ == "__main__":
    import cv2
    detector = FaceDetector()
    frame = cv2.imread("girl.png")
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    # frame = torch.from_numpy(frame)
    # frame = frame.permute(2, 0, 1).contiguous()
    bbox, lmk = detector(frame)
    print(bbox, lmk)
    mask = draw_mouth_masks(lmk,frame)
    cv2.imwrite("mask.png", mask)
    # draw the lmk and bbox into the image and export it to a pdf file using matplotlib and save it as a pdf file.
    # the landmark must contain the order index text
    # import matplotlib.pyplot as plt
    # import matplotlib.patches as patches
    # fig, ax = plt.subplots()
    # ax.imshow(frame)
    # rect = patches.Rectangle((bbox[0], bbox[1]), bbox[2] - bbox[0], bbox[3] - bbox[1], linewidth=1, edgecolor="g", facecolor="none")
    # ax.add_patch(rect)
    # for i, l in enumerate(lmk):
    #     ax.text(l[0], l[1], str(i), fontsize=2, color="b")
    #     # draw a circle around the landmark
    #     circle = plt.Circle((l[0], l[1]), 1, color="r", fill=False)
    #     ax.add_patch(circle)
    # # ax.scatter(lmk[:, 0], lmk[:, 1], s=1, c="r")
    # plt.savefig("insightface_106.pdf")
