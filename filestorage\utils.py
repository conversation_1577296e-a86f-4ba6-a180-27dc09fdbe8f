def format_file_size(num):
    """
    文件大小格式化输出
    :param num: 文件大小 单位字节
    :return: 格式化文件大小 单位KB
    """
    if num < 1024:
        return f'{num}B'
    elif num < 1048576:
        return f'{num // 1024}Kb'
    return f'{num // 1048576}Mb'


def to_string(data):
    """若输入为bytes，则认为是utf-8编码，并返回str"""
    if isinstance(data, bytes):
        return data.decode('utf-8')
    else:
        return data

