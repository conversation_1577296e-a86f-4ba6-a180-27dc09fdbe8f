# Generated by Django 2.2 on 2023-05-07 15:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OSSFileStorage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.CharField(max_length=200, verbose_name='file path')),
                ('data', models.BinaryField(verbose_name='file binary data')),
                ('size', models.IntegerField(blank=True, default=0)),
            ],
        ),
    ]
