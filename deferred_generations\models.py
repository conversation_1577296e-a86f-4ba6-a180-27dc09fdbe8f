from django.db import models

# 状态选择
STATUS_WAITING = 'waiting'
STATUS_INFERENCE = 'inference'
STATUS_FINISHED = 'finished'
STATUS_FAILED = 'failed'

STATUS_CHOICES = [
    (STATUS_WAITING, '等待中'),
    (STATUS_INFERENCE, '处理中'),
    (STATUS_FINISHED, '已完成'),
    (STATUS_FAILED, '失败'),
]


class DeferredGenerationTask(models.Model):
    user_id = models.IntegerField(default=0, blank=True)
    username = models.CharField(max_length=500, default='', blank=True)

    source_audio = models.CharField(max_length=1000)  # maybe url or local path
    source_video = models.Char<PERSON>ield(max_length=1000)  # same as above

    generated_video_path = models.CharField(max_length=500, blank=True, default='')
    status = models.CharField(max_length=32, choices=STATUS_CHOICES, default=STATUS_WAITING)

    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'deferred_generation_task'
        verbose_name = '延迟生成任务'
        verbose_name_plural = '延迟生成任务'
        ordering = ['-created_at']

    def __str__(self):
        return f'Task {self.id} - {self.username} - {self.status}'
