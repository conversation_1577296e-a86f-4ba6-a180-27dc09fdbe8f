from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication
from common_rest.views import CustomizedAPIView, expose
from rest_auth_common.token_auth_authentication import TokenAuthentication

from .models import DeferredGenerationTask, STATUS_WAITING, STATUS_FAILED
from .filters import DeferredGenerationTaskFilter
from filestorage.models import OSSFileStorage


class DeferredGenerationTaskView(CustomizedAPIView):
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    permission_classes = [IsAuthenticated]

    model = DeferredGenerationTask
    filter_class = DeferredGenerationTaskFilter

    def post(self, request, action=None):
        if action == 'batch_create':
            return self.batch_create(request)
        return self.create_single_task(request)

    def create_single_task(self, request):
        """创建单个任务"""
        data = request.data

        source_video = None
        source_audio = None

        # 检查是否通过资源中心选择
        if data.get('source_video_id') and data.get('source_audio_id'):
            try:
                # 获取用户的视频文件
                video_file = OSSFileStorage.objects.get(
                    id=data['source_video_id'],
                    user_id=request.user.id,
                    media_type='video'
                )
                source_video = video_file.url

                # 获取用户的音频文件
                audio_file = OSSFileStorage.objects.get(
                    id=data['source_audio_id'],
                    user_id=request.user.id,
                    media_type='audio'
                )
                source_audio = audio_file.url

            except OSSFileStorage.DoesNotExist:
                return Response({
                    'error_code': 400,
                    'error_reason': '选择的文件不存在或无权限访问'
                }, status=status.HTTP_400_BAD_REQUEST)

        else:
            # 手动输入模式
            source_video = data.get('source_video')
            source_audio = data.get('source_audio')

        # 验证必需字段
        if not source_video or not source_audio:
            return Response({
                'error_code': 400,
                'error_reason': '缺少必需的字段: source_video 和 source_audio'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建任务
        task = DeferredGenerationTask.objects.create(
            user_id=request.user.id if request.user.is_authenticated else 0,
            username=request.user.username if request.user.is_authenticated else '',
            source_video=source_video,
            source_audio=source_audio,
        )

        return Response({
            'error_code': 0,
            'error_reason': 'ok',
            'data': {
                'id': task.id,
                'status': task.status,
                'created_at': task.created_at.isoformat()
            }
        })

    def batch_create(self, request):
        """批量创建任务"""
        data = request.data.get('data', [])

        if not data:
            return Response({
                'error_code': 400,
                'error_reason': '没有提供任务数据'
            }, status=status.HTTP_400_BAD_REQUEST)

        created_tasks = []
        for task_data in data:
            if task_data.get('source_video') and task_data.get('source_audio'):
                task = DeferredGenerationTask.objects.create(
                    user_id=request.user.id if request.user.is_authenticated else 0,
                    username=request.user.username if request.user.is_authenticated else '',
                    source_video=task_data['source_video'],
                    source_audio=task_data['source_audio'],
                )
                created_tasks.append(task)

        return Response({
            'error_code': 0,
            'error_reason': 'ok',
            'data': {
                'created': len(created_tasks),
                'tasks': [{'id': task.id, 'status': task.status} for task in created_tasks]
            }
        })

    def delete(self, request):
        """删除任务"""
        task_id = request.data.get('id')

        if not task_id:
            return Response({
                'error_code': 400,
                'error_reason': '缺少任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            task = DeferredGenerationTask.objects.get(id=task_id)

            # 检查权限（只能删除自己的任务，或管理员可以删除所有任务）
            if not request.user.is_staff and task.user_id != request.user.id:
                return Response({
                    'error_code': 403,
                    'error_reason': '没有权限删除此任务'
                }, status=status.HTTP_403_FORBIDDEN)

            task.delete()

            return Response({
                'error_code': 0,
                'error_reason': 'ok'
            })

        except DeferredGenerationTask.DoesNotExist:
            return Response({
                'error_code': 404,
                'error_reason': '任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

    @expose(['POST'])
    def retry(self, request):
        """重试失败的任务"""
        task_id = request.data.get('id')

        if not task_id:
            return Response({
                'error_code': 400,
                'error_reason': '缺少任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            task = DeferredGenerationTask.objects.get(id=task_id)

            # 检查权限（只能重试自己的任务，或管理员可以重试所有任务）
            if not request.user.is_staff and task.user_id != request.user.id:
                return Response({
                    'error_code': 403,
                    'error_reason': '没有权限重试此任务'
                }, status=status.HTTP_403_FORBIDDEN)

            # 只有失败的任务才能重试
            if task.status != STATUS_FAILED:
                return Response({
                    'error_code': 400,
                    'error_reason': '只有失败的任务才能重试'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 重置任务状态为等待中，清空生成的视频路径
            task.status = STATUS_WAITING
            task.generated_video_path = ''
            task.save()

            return Response({
                'error_code': 0,
                'error_reason': 'ok',
                'data': {
                    'id': task.id,
                    'status': task.status
                }
            })

        except DeferredGenerationTask.DoesNotExist:
            return Response({
                'error_code': 404,
                'error_reason': '任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)
