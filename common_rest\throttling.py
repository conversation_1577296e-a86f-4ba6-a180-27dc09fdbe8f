# coding=utf-8
import six
import re
from django.utils.functional import cached_property
from django.core.exceptions import ImproperlyConfigured

from rest_framework.throttling import SimpleRateThrottle, ScopedRateThrottle

DURATION = {
    'second': 1,
    'minute': 60,
    'hour': 60 * 60,
    'day': 60 * 60 * 24,
}

phone_pattern = re.compile(r"^1\d{10}$")


class ApiScopedRelatedThrottle(SimpleRateThrottle):
    """
        各个api接口独立计数

        配置:
            1: 在view添加 ApiScopedRelatedThrottle
                - 方案1: 在`settings.REST_FRAMEWORK`里面加入DEFAULT_THROTTLE_CLASSES = (ApiScopedRelatedThrottle, otherThrottle......)
                - 方案2: 在view里面添加throttle_classes = (ApiScopedRelatedThrottle, otherThrottle......)
            2: 在`settings.REST_FRAMEWORK`里添加 DEFAULT_THROTTLE_RATES
                ```
                    DEFAULT_THROTTLE_RATES = {
                        'scope1': '200/second',
                        'scope2': '300/minute'
                    }
                ```

                这个也允许在view里面设置THROTTLE_RATES(参考rest_framework), 但是推荐在settings里统一配置

            3: 给view添加类属性api_throttle_scope, 不添加的默认不限流
                api_throttle_scope = {
                    'GET': ['scope1'],
                    'POST': ['scope1', 'scope2'],
                    'action_name': {
                        'GET':  'scope2',  # 如果只需要一个, 可以直接传字符串
                        'POST': ['scope2']  # 一般情况传一个list
                    }
                }
    """
    cache_format = 'throttle_%(scope)s_%(ident)s_%(api_throttling_name)s'

    def __init__(self):
        pass

    def allow_request(self, request, view):
        # We can only determine the scope once we're called by the view.
        api_scope_conf = self.get_api_scope_conf(request, view)

        # If a view does not have a `throttle_scope` always allow the request
        if not api_scope_conf:
            return True

        if isinstance(api_scope_conf, six.string_types):
            api_scope_conf = [api_scope_conf]

        for scope in api_scope_conf:
            # Determine the allowed request rate as we normally would during
            # the `__init__` call.
            self.scope = scope
            self.rate = self.get_rate()
            self.num_requests, self.duration = self.parse_rate(self.rate)

            # We can now proceed as normal.
            if not super(ApiScopedRelatedThrottle, self).allow_request(request, view):
                return False
        return True

    def get_cache_key(self, request, view):
        """
        If `view.throttle_scope` is not set, don't apply this throttle.

        Otherwise generate the unique cache key by concatenating the user id
        with the '.throttle_scope` property of the view.
        """
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)

        return self.cache_format % {
            'scope': self.scope,
            'ident': ident,
            'api_throttling_name': self.api_throttling_name(request, view)
        }

    @staticmethod
    def api_throttling_name(request, view):
        return str(view.action_name) + '_' + request.method.upper()

    @staticmethod
    def get_api_scope_conf(request, view):
        scope_conf = getattr(view, 'api_throttle_scope', {})
        if not scope_conf:
            return None
        if view.action_name is None:
            return scope_conf.get(request.method.upper(), None)
        else:
            scope_conf = scope_conf.get(view.action_name, {})
            return scope_conf.get(request.method.upper(), None)


class ComplexRateThrottle(SimpleRateThrottle):
    def __init__(self):
        self.now = None
        self._rate = None

    @cached_property
    def rate(self):

        if not getattr(self, '_rate', None):
            self._rate = self.get_rate()
        if not isinstance(self._rate, dict):
            raise Exception('频率格式有误，必须是字典类型')
        for k in list(self._rate.keys()):
            if k not in DURATION:
                raise Exception('频率格式有误，key=second OR minute OR hour OR day')
        return self._rate

    def get_rate(self):
        """
        Determine the string representation of the allowed request rate.
        """
        if not getattr(self, 'scope', None):
            msg = ("You must set either `.scope` or `.rate` for '%s' throttle" %
                   self.__class__.__name__)
            raise ImproperlyConfigured(msg)

        try:
            from rest_framework.settings import api_settings
            return api_settings.DEFAULT_THROTTLE_RATES[self.scope]
        except KeyError:
            msg = "No default throttle rate set for '%s' scope" % self.scope
            raise ImproperlyConfigured(msg)

    def allow_request(self, request, view):
        if self.rate is None:
            return True
        key = self.get_cache_key(request, view)
        if key is None:
            return True
        self.now = self.timer()
        for k in list(self.rate.keys()):
            attr_key = key + '_' + k
            history = self.cache.get(attr_key, [])

            if len(history) >= self.rate[k]:
                return False
            self.throttle_success_new(attr_key, history)
        return True

    def throttle_success_new(self, attr_key, history):
        history.insert(0, self.now)
        self.cache.set(attr_key, history, DURATION[attr_key.split('_')[-1]])

    def wait(self):
        return None

    def get_cache_key(self, request, view):
        raise NotImplementedError('.get_cache_key() must be overridden')


class IPThrottle(ComplexRateThrottle):
    scope = 'ip_throttle'

    def allow_request(self, request, view):
        x_real_ip = request.META.get('HTTP_X_REAL_IP', '')
        if any([x_real_ip.startswith('192.168.'), x_real_ip.startswith('127.0.0.1')]):
            return True
        return super(IPThrottle, self).allow_request(request, view)

    def get_cache_key(self, request, view):

        return self.cache_format % {
            'scope': self.scope,
            'ident': self.get_ident(request)
        }


class PhoneThrottle(ComplexRateThrottle):
    scope = 'phone_throttle'

    def get_ident(self, request):
        return request.data['phone']

    def allow_request(self, request, view):
        if request.data.get('phone', None) is None:
            return True

        if phone_pattern.match(request.data['phone']) is None:
            return True
        return super(PhoneThrottle, self).allow_request(request, view)

    def get_cache_key(self, request, view):

        return self.cache_format % {
            'scope': self.scope,
            'ident': self.get_ident(request)
        }