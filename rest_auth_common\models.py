'''
Created on Dec 24, 2015

@author: xiongxt
'''

import time
import hmac
import base64
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.conf import settings

from .settings import api_settings

import logging
logger = logging.getLogger('common.logger')


class AutoExpiredAuthToken(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    key = models.CharField(max_length=40)
    create_time = models.DateTimeField(auto_now_add=True)
    update_time = models.DateTimeField(auto_now=True)
    expire_time = models.DateTimeField()

    @property
    def expire_in(self):
        return 0 if self.expire_time is None else (self.expire_time - timezone.now()).total_seconds()

    def __unicode__(self):
        return self.user_id


class AppIdSecret(models.Model):
    user = models.OneToOneField(User, on_delete=models.DO_NOTHING)
    app_id = models.Char<PERSON><PERSON>(max_length=24)
    app_secret = models.CharField(max_length=30)

    @property
    def access_token_list(self):
        access_token_list = []
        key = ''.join([self.app_id, self.app_secret]).encode('utf-8')

        # for i in range(-10, 10):
        for i in range(-600, 600):
            ts = str(int(time.time()+i)).encode('utf-8')
            token = hmac.new(key, ts, 'MD5').hexdigest().encode('utf-8')
            token = base64.b64encode(token)
            access_token_list.append(token.decode("utf-8"))
        return access_token_list

    def __unicode__(self):
        return "_".join([self.user_id, self.app_id, self.app_secret])


class EnterpriseAppIdSecret(models.Model):
    enterprise_name = models.CharField(max_length=100, unique=True)
    app_id = models.CharField(max_length=24, unique=True)
    app_secret = models.CharField(max_length=30)
    create_time = models.DateTimeField(auto_now_add=True)

    def access_token_list(self, username):
        access_token_list = []
        key = ''.join([self.app_id, self.app_secret, username]).encode('utf-8')

        # for i in range(-10, 10):
        for i in range(-600, 600):
            ts = str(int(time.time()+i)).encode('utf-8')
            token = hmac.new(key, ts, 'MD5').hexdigest().encode('utf-8')
            token = base64.b64encode(token)
            access_token_list.append(token.decode("utf-8"))
        return access_token_list

    def access_token_is_valid(self, username, access_token, ts):
        # use client ts
        if ts != 0:
            return self._access_token_is_valid_with_ts(username, access_token, ts)

        # use server ts
        cts = int(time.time())
        key = ''.join([self.app_id, self.app_secret, username]).encode('utf-8')
        access_token_timeout_ts = abs(int(api_settings.ACCESS_TOKEN_TIMEOUT_TS))
        for deviation in range(access_token_timeout_ts):
            fts = str(cts - deviation).encode('utf-8')
            bts = str(cts + deviation).encode('utf-8')
            if access_token in [
                base64.b64encode(hmac.new(key, fts, 'MD5').hexdigest().encode('utf-8')).decode('utf-8'),
                base64.b64encode(hmac.new(key, bts, 'MD5').hexdigest().encode('utf-8')).decode('utf-8'),
                ]:
                return True
        return False

    def _access_token_is_valid_with_ts(self, username, access_token, ts):
        key = ''.join([self.app_id, self.app_secret, username]).encode('utf-8')
        ts = str(ts).encode('utf-8')
        server_access_token = base64.b64encode(hmac.new(key, ts, 'MD5').hexdigest().encode('utf-8')).decode('utf-8')
        if access_token == server_access_token:
            return True
        return False

    def __unicode__(self):
        return "%s_%s"% (enterprise_name, app_id)

    class Meta(object):
        verbose_name = "Enterprise(multiuser) Secret"
        verbose_name_plural = "Enterprise(multiuser) Secret"


class UserEnterpriseAppIdSecret(models.Model):
    user = models.ForeignKey(User, related_name='user_enterprise_appid', on_delete=models.DO_NOTHING)
    enterprise_appid = models.ForeignKey(EnterpriseAppIdSecret, related_name='user_enterprise_appid', on_delete=models.DO_NOTHING)


class RsaKeyPair(models.Model):
    public_key = models.TextField()
    private_key = models.TextField()
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta(object):
        verbose_name = "Rsa Key Pair"
        verbose_name_plural = "Rsa Key Pair"

