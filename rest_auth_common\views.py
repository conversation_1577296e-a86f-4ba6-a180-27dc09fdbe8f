# -*- coding: utf-8 -*-
import json
import uuid
import base64
import jinja2
from Cryptodome.PublicKey import RSA
from django.conf import settings
from django.core.cache import caches
from django.contrib.auth import authenticate
from django.contrib.auth.models import User

from common_rest.views import CustomizedAPIView, expose, Response
from common_rest.exceptions import ParameterError

from .utils import RestAuthHmyUtils, gen_pwd, decrypt_pwd, random_char_challenge, get_cache_key
from .models import (
    AppIdSecret,
    EnterpriseAppIdSecret,
    UserEnterpriseAppIdSecret,
    RsaKeyPair,
)
from .settings import api_settings, SINGLE_SIGN_ON_ONLY, ERROR_MESSAGES, MESSAGE_TITLE
from . import captcha

cache = caches[api_settings.CAPTCHA_CACHE]


class CaptchaView(CustomizedAPIView):
    def post(self, request):
        key = str(uuid.uuid4())
        value = random_char_challenge(api_settings.CAPTCHA_LENGTH)
        cache_key = get_cache_key(key)
        cache.set(cache_key, value, api_settings.CAPTCHA_TIMEOUT)

        image_bytes = captcha.generate_image(value)
        image_b64 = base64.b64encode(image_bytes)
        data = {
            "captcha_key": key,
            "captcha_image": image_b64,
            "image_type": "image/png",
            "image_decode": "base64",
        }
        return Response({
            "error_reason": "ok",
            "error_code": 0,
            "data": data,
        })


class RsaKeyPairView(CustomizedAPIView):
    @expose(['GET'])
    def get_public_key(self, request):
        try:
            rkp_obj = RsaKeyPair.objects.order_by('?')[0]
        except IndexError:
            key_size = 1024
            key_pair = RSA.generate(key_size)
            publickey = key_pair.publickey()
            publickey_str = publickey.export_key().decode("utf-8")

            private_key_str = key_pair.export_key().decode("utf-8")
            rkp_obj = RsaKeyPair.objects.create(
                public_key=publickey_str,
                private_key=private_key_str
            )
        return Response({
            "error_reason": "ok",
            "error_code": 0,
            "data": {"id": rkp_obj.pk, "public_key": rkp_obj.public_key}
        })

    @expose(['POST'])
    def create_key_pair(self, request):
        key_size = 1024
        key_pair = RSA.generate(key_size)
        publickey = key_pair.publickey()
        publickey_str = publickey.export_key().decode("utf-8")

        private_key_str = key_pair.export_key().decode("utf-8")
        RsaKeyPair.objects.create(
            public_key=publickey_str,
            private_key=private_key_str
        )
        return Response({"error_reason": "ok", "error_code": 0})


class Auth(CustomizedAPIView):
    @expose(['POST'])
    def rsa_login(self, request):
        auth_data = request.data['auth_data']
        rsa_key_id = request.data['rsa_key_id']
        auth_data = decrypt_pwd(rsa_key_id, auth_data)
        auth_data_dict = json.loads(auth_data)  # {"u": "u", "p": "p"}

        captcha_key = request.data['captcha_key']
        cache_key = get_cache_key(captcha_key)
        saved_captcha_value = cache.get(cache_key)
        if captcha_key in api_settings.MASTER_CAPTCHA:
            saved_captcha_value = api_settings.MASTER_CAPTCHA[captcha_key]
        if saved_captcha_value is None:
            return Response({"error_reason": ERROR_MESSAGES['wrong_captcha'], "error_code": 10000})

        cache.delete(cache_key)
        captcha_value = auth_data_dict['captcha_value']
        if captcha_value.lower() != saved_captcha_value.lower():
            return Response({"error_reason": ERROR_MESSAGES['captcha_error'], "error_code": 10001})

        username = auth_data_dict["username"]
        password = auth_data_dict["password"]

        # check locked user
        ## password wrong for 5 times will be locked for 30mins
        locked_user_timeout = int(api_settings.LOCK_USER_TIMEOUT_TS)
        locked_user_key = 'locked_user_%s' % username
        locked_user_value = cache.get(locked_user_key, 0)
        if locked_user_value == 5:
            return Response({
                "error_reason": jinja2.Template(ERROR_MESSAGES['user_locked']).render(username=username),
                "error_code": 10002
            })

        user = authenticate(username=username, password=password)
        if user is None:
            locked_user_value += 1
            cache.set(locked_user_key, locked_user_value, locked_user_timeout)
            return Response({
                "error_reason": jinja2.Template(ERROR_MESSAGES['wrong_password']).render(username=username),
                "error_code": 10003
            })
        cache.delete(locked_user_key)
        data = RestAuthHmyUtils.login_and_get_response_data(user, refresh=SINGLE_SIGN_ON_ONLY)
        data["username"] = username
        extra_data = {"username": username}
        if hasattr(user, "account"):
            extra_obj = getattr(user, "account")
            extra_data["name"] = extra_obj.name
            extra_data["phone_number"] = extra_obj.phone_number
        data["extra_data"] = extra_data
        return Response({
            "error_reason": "ok",
            "error_code": 0,
            "data": data,
        })

    @expose(['POST'])
    def password_login(self, request):
        username = request.data['username']
        password = request.data.get('password', None)

        rsa_key_id = request.data.get('rsa_key_id', None)
        if rsa_key_id is not None:
            password = decrypt_pwd(rsa_key_id, password)

        user = authenticate(username=username, password=password)
        if user is None:
            return Response({
                "error_reason": jinja2.Template(ERROR_MESSAGES['wrong_password']).render(username=username),
                "error_code": 10003
            })
        data = RestAuthHmyUtils.login_and_get_response_data(user, refresh=SINGLE_SIGN_ON_ONLY)
        data["username"] = username
        extra_data = {"username": username}
        if hasattr(user, "account"):
            extra_obj = getattr(user, "account")
            extra_data["name"] = extra_obj.name
            extra_data["phone_number"] = extra_obj.phone_number
            extra_data["permission"] = extra_obj.permission
        data["extra_data"] = extra_data
        return Response({
            "error_reason": "ok",
            "error_code": 0,
            "data": data,
        })

    @expose(['POST'])
    def change_password(self, request):
        username = request.user.username
        current_password = request.data.get('current_password', None)
        password1 = request.data.get('password1', None)
        password2 = request.data.get('password2', None)

        rsa_key_id = request.data.get('rsa_key_id', None)
        if rsa_key_id is not None:
            current_password = decrypt_pwd(rsa_key_id, current_password)
            password1 = decrypt_pwd(rsa_key_id, password1)
            password2 = decrypt_pwd(rsa_key_id, password2)

        user = authenticate(username=username, password=current_password)
        if user is None:
            return Response({
                "error_reason": jinja2.Template(ERROR_MESSAGES['wrong_password']).render(username=username),
                "error_code": 10003
            })
        if len(password1) < 6:
            raise ParameterError("Password's length must be more than 6")
        if password1 != password2:
            raise ParameterError("The two passwords didn't match.")
        user.set_password(password2)
        user.save()
        data = RestAuthHmyUtils.login_and_get_response_data(user, refresh=True)
        return Response({
            "error_reason": "ok",
            "error_code": 0,
            "data": data
        })

    @expose(['POST'])
    def create_enterprise_app_id(self, request):
        enterprise_name = request.data['enterprise_name']
        app_id = gen_pwd(24)
        app_secret = gen_pwd(30)
        enterprise_app_id_secret_obj, created = EnterpriseAppIdSecret.objects.get_or_create(
            enterprise_name=enterprise_name, defaults={
                "app_id": app_id,
                "app_secret": app_secret,
            })
        return Response({"message": "ok", "error_code": 0, "data": {
            "enterprise_name": enterprise_app_id_secret_obj.enterprise_name,
            "app_id": enterprise_app_id_secret_obj.app_id,
            "app_secret": enterprise_app_id_secret_obj.app_secret,
        }})

    @expose(['POST'])
    def eaccess_token_login(self, request):
        # enterprise access token login
        app_id = request.data['app_id']
        username = request.data['username']
        access_token = request.data['access_token']
        enterprise_app_id_secret_obj = EnterpriseAppIdSecret.objects.get(app_id=app_id)

        # if access_token in enterprise_app_id_secret_obj.access_token_list(username):
        ts = request.data.get('ts', 0)  # client ts
        ts = int(ts)
        if enterprise_app_id_secret_obj.access_token_is_valid(username, access_token, ts):
            enterprise_username = "_".join([enterprise_app_id_secret_obj.enterprise_name, username])
            user, created = User.objects.get_or_create(username=enterprise_username, defaults={"password": gen_pwd(12)})
            user_e_app_id, created = UserEnterpriseAppIdSecret.objects.get_or_create(user=user,
                                                                                     enterprise_appid=enterprise_app_id_secret_obj)
            return Response(RestAuthHmyUtils.login_and_get_response_data(user, refresh=SINGLE_SIGN_ON_ONLY))
        return Response(
            {MESSAGE_TITLE: ERROR_MESSAGES['eaccess_token_not_match'], 'error_code': 10004})

    # ==========DISUSE API=====================

    @expose(['POST'])
    def create_user_and_app_id(self, request):
        username = request.data['username']
        user, created = User.objects.get_or_create(username=username, defaults={"password": gen_pwd(12)})
        app_id = gen_pwd(24)
        app_secret = gen_pwd(30)
        app_id_secret_obj, created = AppIdSecret.objects.get_or_create(user_id=user.id, defaults={
            "app_id": app_id,
            "app_secret": app_secret,
        })
        return Response({"message": "ok", "error_code": 0,
                         "data": {"app_id": app_id_secret_obj.app_id, "app_secret": app_id_secret_obj.app_secret}})

    @expose(['POST'])
    def create_app_id(self, request):
        username = request.data['username']
        password = request.data.get('password', None)

        rsa_key_id = request.data.get('rsa_key_id', None)
        if rsa_key_id is not None:
            password = decrypt_pwd(rsa_key_id, password)

        user = authenticate(username=username, password=password)
        if user is None:
            return Response({
                MESSAGE_TITLE: jinja2.Template(ERROR_MESSAGES['wrong_password']).render(username=username),
                "error_code": 10003
            })

        app_id = gen_pwd(24)
        app_secret = gen_pwd(30)
        app_id_secret_obj, created = AppIdSecret.objects.get_or_create(user_id=user.id, defaults={
            "app_id": app_id,
            "app_secret": app_secret,
        })
        return Response({"message": "ok", "error_code": 0,
                         "data": {"app_id": app_id_secret_obj.app_id, "app_secret": app_id_secret_obj.app_secret}})

    @expose(['POST'])
    def access_token_login(self, request):
        app_id = request.data['app_id']
        app_secret = request.data['app_secret']
        access_token = request.data['access_token']
        app_id_secret_obj = AppIdSecret.objects.get(app_id=app_id, app_secret=app_secret)

        if access_token in app_id_secret_obj.access_token_list:
            user = app_id_secret_obj.user
            return Response(RestAuthHmyUtils.login_and_get_response_data(user, refresh=SINGLE_SIGN_ON_ONLY))
        return Response({MESSAGE_TITLE: ERROR_MESSAGES['eaccess_token_not_match'], 'error_code': 10004})
