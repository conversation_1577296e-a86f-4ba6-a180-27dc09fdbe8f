# Generated by Django 2.0.2 on 2020-03-19 08:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rest_auth_common', '0002_appidsecret'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnterpriseAppIdSecret',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enterprise_name', models.CharField(max_length=100, unique=True)),
                ('app_id', models.CharField(max_length=24, unique=True)),
                ('app_secret', models.CharField(max_length=30)),
                ('create_time', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Enterprise(multiuser) Secret',
                'verbose_name_plural': 'Enterprise(multiuser) Secret',
            },
        ),
        migrations.CreateModel(
            name='UserEnterpriseAppIdSecret',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enterprise_appid', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='user_enterprise_appid', to='rest_auth_common.EnterpriseAppIdSecret')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='user_enterprise_appid', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
