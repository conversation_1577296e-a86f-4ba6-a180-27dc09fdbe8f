{% extends 'base.html' %}
{% load static %}

{% block title %}资源中心{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-folder-open me-2"></i>资源中心</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload me-1"></i>上传文件
                    </button>
                </div>

                <div class="card-body">
                    <!-- 搜索和过滤 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="mediaTypeFilter">
                                <option value="">全部类型</option>
                                <option value="video">视频</option>
                                <option value="audio">音频</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索文件名或描述...">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-primary w-100" onclick="searchFiles()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>

                    <!-- 文件列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                    <th>描述</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="fileList">
                                <!-- 文件列表将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav id="pagination" style="display: none;">
                        <ul class="pagination justify-content-center" id="paginationList">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传文件模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-upload me-2"></i>上传文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">素材类型 <span class="text-danger">*</span></label>
                        <select class="form-select" name="media_type" required>
                            <option value="">请选择类型</option>
                            <option value="video">视频</option>
                            <option value="audio">音频</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" name="upload_file" required>
                        <div class="form-text">
                            视频支持: MP4, AVI, MOV, MKV, WMV<br>
                            音频支持: MP3, WAV, AAC, FLAC, OGG
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3" placeholder="可选，描述这个文件的用途或内容"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="fas fa-upload me-1"></i>上传
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentMediaType = '';
let currentSearch = '';

// 页面加载时获取文件列表
$(document).ready(function() {
    loadFiles();
});

// 加载文件列表
function loadFiles(page = 1) {
    currentPage = page;

    $.ajax({
        url: '/file/media-center/',
        type: 'GET',
        data: {
            page: page,
            media_type: currentMediaType,
            search: currentSearch
        },
        success: function(response) {
            if (response.error_code === 0) {
                renderFileList(response.data.files);
                renderPagination(response.data);
            } else {
                showAlert('danger', '加载失败: ' + response.error_reason);
            }
        },
        error: function(xhr) {
            showAlert('danger', '请求失败: ' + xhr.responseText);
        }
    });
}

// 渲染文件列表
function renderFileList(files) {
    const tbody = $('#fileList');
    tbody.empty();

    if (files.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-3x mb-3"></i><br>
                    暂无文件，点击上传按钮添加文件
                </td>
            </tr>
        `);
        return;
    }

    files.forEach(file => {
        const typeIcon = file.media_type === 'video' ? 'fas fa-video text-primary' : 'fas fa-music text-success';
        tbody.append(`
            <tr>
                <td>
                    <i class="${typeIcon} me-2"></i>
                    ${file.original_name}
                </td>
                <td><span class="badge bg-${file.media_type === 'video' ? 'primary' : 'success'}">${file.media_type_display}</span></td>
                <td>${file.size_format}</td>
                <td>${file.description || '-'}</td>
                <td>${file.created_at}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="previewFile(${file.id}, '${file.url}', '${file.media_type}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteFile(${file.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

// 搜索文件
function searchFiles() {
    currentMediaType = $('#mediaTypeFilter').val();
    currentSearch = $('#searchInput').val();
    loadFiles(1);
}

// 上传文件
function uploadFile() {
    const formData = new FormData($('#uploadForm')[0]);

    $.ajax({
        url: '/file/media-center/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.error_code === 0) {
                showAlert('success', '文件上传成功！');
                $('#uploadModal').modal('hide');
                $('#uploadForm')[0].reset();
                loadFiles(1);
            } else {
                showAlert('danger', '上传失败: ' + response.error_reason);
            }
        },
        error: function(xhr) {
            showAlert('danger', '上传失败: ' + xhr.responseText);
        }
    });
}

// 删除文件
function deleteFile(fileId) {
    if (confirm('确定要删除这个文件吗？删除后无法恢复。')) {
        $.ajax({
            url: '/file/media-center/delete_media/',
            type: 'POST',
            data: JSON.stringify({id: fileId}),
            contentType: 'application/json',
            success: function(response) {
                if (response.error_code === 0) {
                    showAlert('success', '文件删除成功！');
                    loadFiles(currentPage);
                } else {
                    showAlert('danger', '删除失败: ' + response.error_reason);
                }
            },
            error: function(xhr) {
                showAlert('danger', '删除失败: ' + xhr.responseText);
            }
        });
    }
}

// 预览文件
function previewFile(fileId, url, mediaType) {
    // 这里可以实现文件预览功能
    window.open(url, '_blank');
}

// 渲染分页
function renderPagination(data) {
    const pagination = $('#pagination');
    const paginationList = $('#paginationList');

    if (data.total_pages <= 1) {
        pagination.hide();
        return;
    }

    pagination.show();
    paginationList.empty();

    // 上一页
    if (data.page > 1) {
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadFiles(${data.page - 1})">上一页</a>
            </li>
        `);
    }

    // 页码
    for (let i = Math.max(1, data.page - 2); i <= Math.min(data.total_pages, data.page + 2); i++) {
        paginationList.append(`
            <li class="page-item ${i === data.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadFiles(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    if (data.page < data.total_pages) {
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadFiles(${data.page + 1})">下一页</a>
            </li>
        `);
    }
}

// 显示提示信息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}