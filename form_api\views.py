import logging
import os
import requests
import uuid
from urllib.parse import urlparse
from omegaconf import OmegaConf
from django.conf import settings
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from common_rest.views import CustomizedAPIView, expose
from utils.standard_response import format_response
from . import serializers, models
from .tools import inference

logger = logging.getLogger('common.logger')


dinet_config = OmegaConf.load(settings.DINET_CONFIG_PATH)
inference_ckpt_path = settings.INFERENCE_CKPT_PATH


class InferenceView(CustomizedAPIView):
    """
    视频音频推理接口
    """
    permission_classes = [IsAuthenticated]

    @expose(['POST'])
    @format_response
    def post(self, request):
        """
        视频音频推理接口

        请求参数:
        - video_url: 视频URL
        - audio_url: 音频URL

        返回:
        - error_code: 错误码，0表示成功
        - error_reason: 错误原因，成功时为"ok"
        - data: 结果数据，包含video_url
        """
        try:
            # 验证请求参数
            serializer = serializers.InferenceRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return {
                    'error_code': 400,
                    'error_reason': '参数验证失败',
                    'data': serializer.errors
                }

            validated_data = serializer.validated_data
            video_url = validated_data['video_url']
            audio_url = validated_data['audio_url']

            logger.info(f"开始处理推理请求 - 用户: {request.user.id}, 视频URL: {video_url}, 音频URL: {audio_url}")

            # TODO: 在这里添加实际的推理逻辑
            # 这里只是示例，你需要根据实际业务逻辑来处理视频和音频
            processed_video_url = self._process_inference(video_url, audio_url)

            # 构造返回数据
            response_data = {
                'video_url': processed_video_url
            }

            logger.info(f"推理处理完成 - 用户: {request.user.id}, 结果URL: {processed_video_url}")

            return {
                'error_code': 0,
                'error_reason': 'ok',
                'data': response_data
            }

        except Exception as e:
            logger.error(f"推理处理失败 - 用户: {request.user.id}, 错误: {str(e)}", exc_info=True)
            return {
                'error_code': 500,
                'error_reason': '服务器内部错误',
                'data': {}
            }

    def _process_inference(self, video_url, audio_url):
        """
        处理推理逻辑的私有方法

        Args:
            video_url: 输入视频URL
            audio_url: 输入音频URL

        Returns:
            str: 处理后的视频URL
        """
        try:
            # 确保 /tmp 目录存在
            tmp_dir = "/tmp"
            if not os.path.exists(tmp_dir):
                os.makedirs(tmp_dir)

            # 生成唯一的文件名
            unique_id = str(uuid.uuid4())

            # 下载视频文件
            video_filename = self._download_file(video_url, tmp_dir, f"video_{unique_id}")
            logger.info(f"视频文件下载完成: {video_filename}")

            # 下载音频文件
            audio_filename = self._download_file(audio_url, tmp_dir, f"audio_{unique_id}")
            logger.info(f"音频文件下载完成: {audio_filename}")

            # 生成输出视频文件路径
            output_video_filename = os.path.join(tmp_dir, f"output_{unique_id}.mp4")

            # 调用推理逻辑
            logger.info(f"开始推理处理 - 视频: {video_filename}, 音频: {audio_filename}")
            inference(
                video_path=video_filename,
                audio_path=audio_filename,
                output_path=output_video_filename,
                config=dinet_config,
                checkpoint_path=inference_ckpt_path
            )
            logger.info(f"推理处理完成，输出文件: {output_video_filename}")

            # 验证输出文件是否生成成功
            if not os.path.exists(output_video_filename):
                raise Exception("推理处理失败，未生成输出文件")

            # 创建OUTPUTVIDEOOSSFileStorage记录
            oss_file_record = self._create_oss_file_record(output_video_filename, unique_id)
            logger.info(f"OSS文件记录创建完成 - ID: {oss_file_record.id}, URL: {oss_file_record.url}")

            # 清理临时文件（可选）
            self._cleanup_temp_files([video_filename, audio_filename, output_video_filename])

            return oss_file_record.url

        except Exception as e:
            logger.error(f"推理处理过程中发生错误: {str(e)}", exc_info=True)
            raise

    def _download_file(self, url, save_dir, filename_prefix):
        """
        下载文件到指定目录

        Args:
            url: 文件URL
            save_dir: 保存目录
            filename_prefix: 文件名前缀

        Returns:
            str: 下载后的文件完整路径
        """
        try:
            # 获取文件扩展名
            parsed_url = urlparse(url)
            path = parsed_url.path
            ext = os.path.splitext(path)[1] if path else ""
            if not ext:
                # 如果没有扩展名，根据URL类型猜测
                if "video" in filename_prefix:
                    ext = ".mp4"
                elif "audio" in filename_prefix:
                    ext = ".mp3"
                else:
                    ext = ".tmp"

            # 生成完整文件名
            filename = f"{filename_prefix}{ext}"
            filepath = os.path.join(save_dir, filename)

            # 下载文件
            logger.info(f"开始下载文件: {url} -> {filepath}")
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # 保存文件
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            logger.info(f"文件下载成功: {filepath}, 大小: {os.path.getsize(filepath)} bytes")
            return filepath

        except requests.exceptions.RequestException as e:
            logger.error(f"下载文件失败 {url}: {str(e)}")
            raise Exception(f"下载文件失败: {str(e)}")
        except Exception as e:
            logger.error(f"保存文件失败 {url}: {str(e)}")
            raise Exception(f"保存文件失败: {str(e)}")

    def _cleanup_temp_files(self, file_paths):
        """
        清理临时文件

        Args:
            file_paths: 要删除的文件路径列表
        """
        for filepath in file_paths:
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
                    logger.info(f"临时文件已删除: {filepath}")
            except Exception as e:
                logger.warning(f"删除临时文件失败 {filepath}: {str(e)}")

    def _create_oss_file_record(self, file_path, unique_id):
        """
        创建OSS文件存储记录

        Args:
            file_path: 本地文件路径
            unique_id: 唯一标识符

        Returns:
            OUTPUTVIDEOOSSFileStorage: 创建的文件记录
        """
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # 获取文件大小
            file_size = os.path.getsize(file_path)

            # 生成OSS存储路径
            file_name = f"output_{unique_id}.mp4"
            oss_path = f"inference_results/{file_name}"

            # 创建OSS文件记录
            oss_file_record = models.OUTPUTVIDEOOSSFileStorage(
                path=oss_path,
                data=file_data,
                size=file_size
            )
            oss_file_record.save()

            logger.info(f"OSS文件记录创建成功 - 文件: {file_name}, 大小: {file_size} bytes")
            return oss_file_record

        except Exception as e:
            logger.error(f"创建OSS文件记录失败: {str(e)}", exc_info=True)
            raise Exception(f"创建OSS文件记录失败: {str(e)}")


class DirectInferenceView(CustomizedAPIView):
    """
    直接文件路径推理接口（不使用缓存）
    """
    permission_classes = [IsAuthenticated]

    @expose(['POST'])
    @format_response
    def post(self, request):
        """
        直接文件路径推理接口

        请求参数:
        - video_path: 视频文件路径
        - audio_path: 音频文件路径
        - output_path: 输出视频文件路径

        返回:
        - error_code: 错误码，0表示成功
        - error_reason: 错误原因，成功时为"ok"
        - data: 结果数据，包含output_path
        """
        try:
            # 验证请求参数
            serializer = serializers.DirectInferenceRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return {
                    'error_code': 400,
                    'error_reason': '参数验证失败',
                    'data': serializer.errors
                }

            validated_data = serializer.validated_data
            video_path = validated_data['video_path']
            audio_path = validated_data['audio_path']
            output_path = validated_data['output_path']

            logger.info(f"开始直接路径推理 - 用户: {request.user.id}, 视频: {video_path}, 音频: {audio_path}")

            # 验证输入文件是否存在
            if not os.path.exists(video_path):
                return {
                    'error_code': 404,
                    'error_reason': f'视频文件不存在: {video_path}',
                    'data': {}
                }

            if not os.path.exists(audio_path):
                return {
                    'error_code': 404,
                    'error_reason': f'音频文件不存在: {audio_path}',
                    'data': {}
                }

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 调用推理逻辑（禁用缓存）
            logger.info(f"开始推理处理 - 视频: {video_path}, 音频: {audio_path}")
            result_path = inference(
                config=dinet_config,
                video_path=video_path,
                audio_path=audio_path,
                video_out_path=output_path,
                inference_ckpt_path=inference_ckpt_path,
                enable_cache=False  # 禁用缓存
            )

            logger.info(f"推理处理完成，输出文件: {result_path}")

            # 验证输出文件是否生成成功
            if not os.path.exists(result_path):
                raise Exception("推理处理失败，未生成输出文件")

            # 构造返回数据
            response_data = {
                'output_path': result_path
            }

            logger.info(f"直接路径推理完成 - 用户: {request.user.id}, 输出: {result_path}")

            return {
                'error_code': 0,
                'error_reason': 'ok',
                'data': response_data
            }

        except Exception as e:
            logger.error(f"直接路径推理失败 - 用户: {request.user.id}, 错误: {str(e)}", exc_info=True)
            return {
                'error_code': 500,
                'error_reason': f'推理处理失败: {str(e)}',
                'data': {}
            }
