# -*- coding: utf-8 -*-


import pytz
import datetime

_TIME_ZONE = None


def get_django_timezone():
    global _TIME_ZONE
    from django.conf import settings
    if _TIME_ZONE is None:
        _TIME_ZONE = pytz.timezone(settings.TIME_ZONE)
    return _TIME_ZONE


def get_timestamp(dt):
    if type(dt) == datetime.datetime:
        return int((dt - datetime.datetime(1970, 1, 1, tzinfo=pytz.UTC)).total_seconds())
    elif type(dt) == datetime.date:
        return int((dt - datetime.datetime(1970, 1, 1, tzinfo=pytz.UTC).date()).total_seconds())
    return dt


def get_js_timestamp(dt):
    return 1000 * get_timestamp(dt)


def get_datetime(epoch):
    """
    :type epoch: float
    :rtype : datetime.datetime
    """
    return datetime.datetime.utcfromtimestamp(epoch).replace(tzinfo=pytz.UTC)


def get_datetime_from_local_date_str(date, format_='%Y%m%d', timezone=None):
    if timezone is None:
        timezone = get_django_timezone()
    return timezone.localize(datetime.datetime.strptime(date, format_))


def get_datetime_from_local_date_str_split(date, format_='%Y-%m-%d', timezone=None):
    if timezone is None:
        timezone = get_django_timezone()
    return timezone.localize(datetime.datetime.strptime(date, format_))


def get_datetime_from_local_datetime_str(date, format_='%Y%m%d %H:%M:%S', timezone=None):
    if timezone is None:
        timezone = get_django_timezone()
    return timezone.localize(datetime.datetime.strptime(date, format_))


def get_datetime_from_local_date(date, timezone=None):
    if timezone is None:
        timezone = get_django_timezone()
    return timezone.localize(datetime.datetime.now()).replace(year=date.year,
                                                              month=date.month,
                                                              day=date.day,
                                                              hour=0,
                                                              minute=0,
                                                              second=0,
                                                              microsecond=0,
                                                              )


def truncate_to_last_update_day(dt, hour=0):
    assert isinstance(dt, datetime.datetime)
    # 如果还没到这个事件, 回到上一天的这个点
    if dt.hour < hour:
        dt = dt + datetime.timedelta(days=-1)
    return dt.replace(hour=hour, minute=0, second=0, microsecond=0)


def truncate_to_hourly(dt):
    assert isinstance(dt, datetime.datetime)
    return dt.replace(minute=0, second=0, microsecond=0)


def truncate_to_month(d):
    if isinstance(d, datetime.datetime):
        return d.date().replace(day=1)
    elif isinstance(d, datetime.date):
        return d.replace(day=1)
    raise RuntimeError


def get_now_local_date(timezone=None):
    if timezone is None:
        timezone = get_django_timezone()
    return timezone.localize(datetime.datetime.now()).date()
