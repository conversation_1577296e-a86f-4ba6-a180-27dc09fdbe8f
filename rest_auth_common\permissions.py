# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from __future__ import absolute_import

from rest_framework.permissions import AllowAny, IsAuthenticated, IsAdminUser

CUSTOM_PERMISSIONS = (
    (
        'rest_auth_common.auth', {
            'rsa_login': {'POST': AllowAny},
            'password_login': {'POST': AllowAny},
            'change_password': {'POST': IsAuthenticated},
            'create_user_and_app_id': {'POST': IsAdminUser},
            'create_app_id': {'POST': AllowAny},
            'access_token_login': {'POST': AllowAny},
            'create_enterprise_app_id': {'POST': IsAdminUser},
            'eaccess_token_login': {'POST': AllowAny},
        }
    ),
    (
        'rest_auth_common.rsa', {
            'get_public_key': {'GET': AllowAny},
            'create_key_pair': {'POST': IsAdminUser},
        }
    ),
    (
        'rest_auth_common.captcha', {
            'POST': AllowAny,
        }
    ),
)
