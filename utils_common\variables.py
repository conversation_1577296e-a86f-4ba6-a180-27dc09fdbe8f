# -*- coding: utf-8 -*-
import copy
from django.utils.functional import cached_property


class VAR(dict):
    def __getattr__(self, key):
        if key in self:
            return super(VAR, self).__getitem__(key)
        else:
            raise AttributeError("key %s not found in var dict %s" % (key, self))

    def __setattr__(self, key, value, *args, **kwargs):
        if key in self or not hasattr(self, key):
            return super(VAR, self).__setitem__(key, value)
        else:
            return dict.__setattr__(self, *args, **kwargs)


class DjangoChoicesConfigError(Exception):
    pass


class DjangoChoicesConfig(object):

    def __init__(self, config):
        self.config = self.get_fixed_config(config)

    @staticmethod
    def get_fixed_config(config):
        new_config = []
        name_set = set()
        value_set = set()
        for _ in copy.deepcopy(config):
            if 'name' not in _:
                raise DjangoChoicesConfigError('Each Item of config Must has key [ name ] %s' % _)
            elif _['name'] in name_set:
                raise DjangoChoicesConfigError('Duplicated name [ %s ]' % _)
            else:
                name_set.add(_['name'])

            if 'value' not in _:
                raise DjangoChoicesConfigError('Each Item of config Must has key [ value ]')
            elif _['value'] in value_set:
                raise DjangoChoicesConfigError('Duplicated value [ %s ]' % _)
            else:
                value_set.add(_['value'])

            if 'text' not in _:
                _['text'] = _['name']
            new_config.append(_)
        return new_config

    @cached_property
    def model_choices(self):
        return [(_['value'], _['name']) for _ in self.config]

    @cached_property
    def name_to_config_item(self):
        return {_['name']: _ for _ in self.config}

    @cached_property
    def value_to_config_item(self):
        return {_['value']: _ for _ in self.config}

    def __getattr__(self, name):
        """
            name => value
        """
        if name not in self.name_to_config_item:
            raise AttributeError(name)
        return self.name_to_config_item[name]['value']

    def get_text(self, value):
        """
            value => text
        """
        return self.value_to_config_item[value]['text']

    def get_name(self, value):
        """
            value => text
        """
        return self.value_to_config_item[value]['name']
