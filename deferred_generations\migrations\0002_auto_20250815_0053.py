# Generated by Django 3.2 on 2025-08-14 16:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deferred_generations', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='deferredgenerationtask',
            options={'ordering': ['-created_at'], 'verbose_name': '延迟生成任务', 'verbose_name_plural': '延迟生成任务'},
        ),
        migrations.RemoveField(
            model_name='deferredgenerationtask',
            name='generated_video',
        ),
        migrations.AddField(
            model_name='deferredgenerationtask',
            name='generated_video_path',
            field=models.CharField(blank=True, default='', max_length=500),
        ),
        migrations.AlterField(
            model_name='deferredgenerationtask',
            name='source_audio',
            field=models.CharField(max_length=1000),
        ),
        migrations.AlterField(
            model_name='deferredgenerationtask',
            name='source_video',
            field=models.CharField(max_length=1000),
        ),
        migrations.AlterField(
            model_name='deferredgenerationtask',
            name='status',
            field=models.CharField(choices=[('waiting', '等待中'), ('inference', '处理中'), ('finished', '已完成'), ('failed', '失败')], default='waiting', max_length=32),
        ),
        migrations.AlterModelTable(
            name='deferredgenerationtask',
            table='deferred_generation_task',
        ),
    ]
