import os
import json
import csv
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


class Command(BaseCommand):
    help = 'DINet批量处理命令 - 支持批量推理和任务管理'

    def add_arguments(self, parser):
        parser.add_argument(
            'batch_file',
            help='批量任务文件 (JSON或CSV格式)'
        )
        parser.add_argument(
            '--workers',
            type=int,
            default=1,
            help='并行工作线程数 (默认: 1)'
        )
        parser.add_argument(
            '--output-dir',
            default='./batch_output',
            help='批量输出目录 (默认: ./batch_output)'
        )
        parser.add_argument(
            '--continue-on-error',
            action='store_true',
            help='遇到错误时继续处理其他任务'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不执行实际推理'
        )

    def handle(self, *args, **options):
        batch_file = options['batch_file']
        
        if not os.path.exists(batch_file):
            raise CommandError(f'批量任务文件不存在: {batch_file}')
        
        # 解析批量任务文件
        tasks = self.parse_batch_file(batch_file)
        
        if not tasks:
            raise CommandError('批量任务文件为空或格式错误')
        
        self.stdout.write(f'📋 加载了 {len(tasks)} 个任务')
        
        # 创建输出目录
        output_dir = options['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        if options['dry_run']:
            self.stdout.write('🔍 试运行模式 - 不执行实际推理')
            self.show_tasks_preview(tasks)
            return
        
        # 执行批量处理
        self.execute_batch_tasks(tasks, options)

    def parse_batch_file(self, batch_file):
        """解析批量任务文件"""
        tasks = []
        
        try:
            if batch_file.endswith('.json'):
                with open(batch_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        tasks = data
                    else:
                        tasks = [data]
            
            elif batch_file.endswith('.csv'):
                with open(batch_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    tasks = list(reader)
            
            else:
                raise CommandError('不支持的文件格式，请使用JSON或CSV文件')
        
        except Exception as e:
            raise CommandError(f'解析批量任务文件失败: {e}')
        
        # 验证任务格式
        validated_tasks = []
        for i, task in enumerate(tasks):
            if not all(key in task for key in ['video', 'audio', 'output']):
                self.stdout.write(
                    self.style.WARNING(f'⚠️  跳过任务 {i+1}: 缺少必要字段')
                )
                continue
            validated_tasks.append(task)
        
        return validated_tasks

    def show_tasks_preview(self, tasks):
        """显示任务预览"""
        self.stdout.write('📋 任务预览:')
        self.stdout.write('-' * 80)
        
        for i, task in enumerate(tasks[:10]):  # 只显示前10个
            self.stdout.write(f'任务 {i+1}:')
            self.stdout.write(f'   视频: {task["video"]}')
            self.stdout.write(f'   音频: {task["audio"]}')
            self.stdout.write(f'   输出: {task["output"]}')
            self.stdout.write('')
        
        if len(tasks) > 10:
            self.stdout.write(f'... 还有 {len(tasks) - 10} 个任务')

    def execute_batch_tasks(self, tasks, options):
        """执行批量任务"""
        workers = options['workers']
        output_dir = options['output_dir']
        continue_on_error = options['continue_on_error']
        
        # 结果统计
        results = {
            'success': 0,
            'failed': 0,
            'errors': []
        }
        
        # 创建线程锁用于输出同步
        output_lock = threading.Lock()
        
        def process_single_task(task_info):
            task_id, task = task_info
            
            try:
                # 构建输出路径
                output_path = os.path.join(output_dir, task['output'])
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                with output_lock:
                    self.stdout.write(f'🚀 开始处理任务 {task_id}: {task["video"]}')
                
                # 调用推理命令
                call_command(
                    'dinet_inference',
                    'run',
                    video=task['video'],
                    audio=task['audio'],
                    output=output_path,
                    config=task.get('config', 'form_api/configs/dinet_config.yaml'),
                    checkpoint=task.get('checkpoint', 'form_api/checkpoints/dinet_model.pth'),
                    seed=int(task.get('seed', 1247)),
                    no_cache=task.get('no_cache', False),
                    verbosity=0  # 减少输出
                )
                
                with output_lock:
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ 任务 {task_id} 完成: {output_path}')
                    )
                
                return {'task_id': task_id, 'status': 'success', 'output': output_path}
                
            except Exception as e:
                error_msg = f'任务 {task_id} 失败: {str(e)}'
                
                with output_lock:
                    self.stdout.write(self.style.ERROR(f'❌ {error_msg}'))
                
                if not continue_on_error:
                    raise CommandError(error_msg)
                
                return {'task_id': task_id, 'status': 'failed', 'error': str(e)}
        
        # 执行批量处理
        self.stdout.write(f'🔄 开始批量处理 ({workers} 个工作线程)...')
        
        with ThreadPoolExecutor(max_workers=workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(process_single_task, (i+1, task)): i+1 
                for i, task in enumerate(tasks)
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_task):
                result = future.result()
                
                if result['status'] == 'success':
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(result)
        
        # 输出最终统计
        self.stdout.write('\n' + '='*50)
        self.stdout.write('📊 批量处理完成统计:')
        self.stdout.write(f'   成功: {results["success"]} 个任务')
        self.stdout.write(f'   失败: {results["failed"]} 个任务')
        self.stdout.write(f'   总计: {len(tasks)} 个任务')
        
        if results['errors']:
            self.stdout.write('\n❌ 失败任务详情:')
            for error in results['errors']:
                self.stdout.write(f'   任务 {error["task_id"]}: {error["error"]}')
        
        # 保存结果报告
        report_file = os.path.join(output_dir, 'batch_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        self.stdout.write(f'\n📄 详细报告已保存: {report_file}')