import os
import uuid
import time
import requests
import include
import tempfile
import shutil
import subprocess

import django

django.setup()

import logging
from urllib.parse import urlparse
from omegaconf import OmegaConf
from django.db import connection, transaction
from django.conf import settings
from deferred_generations.models import DeferredGenerationTask, STATUS_WAITING, STATUS_INFERENCE, STATUS_FINISHED, STATUS_FAILED
from form_api.tools import inference

logger = logging.getLogger('common.logger')

dinet_config = OmegaConf.load(settings.DINET_CONFIG_PATH)
inference_ckpt_path = settings.INFERENCE_CKPT_PATH


def is_url(path):
    """检查路径是否为URL"""
    try:
        result = urlparse(path)
        return all([result.scheme, result.netloc])
    except:
        return False


def validate_media_file(file_path, media_type='video'):
    """验证媒体文件是否有效"""
    try:
        if media_type == 'video':
            # 使用ffprobe验证视频文件
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 'v:0', file_path
            ]
        else:  # audio
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 'a:0', file_path
            ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            logger.error(f"ffprobe验证失败: {file_path}, 错误: {result.stderr}")
            return False

        # 检查是否有流信息
        import json
        probe_data = json.loads(result.stdout)
        if not probe_data.get('streams'):
            logger.error(f"媒体文件没有有效的流: {file_path}")
            return False

        logger.info(f"媒体文件验证成功: {file_path}")
        return True

    except Exception as e:
        logger.error(f"验证媒体文件时出错: {file_path}, 错误: {str(e)}")
        return False


def download_file(url, local_path):
    """下载文件到本地路径并验证"""
    logger.info(f"开始下载文件: {url} -> {local_path}")

    try:
        # 添加更多的请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        # 下载文件
        logger.info(f"开始下载文件: {url} -> {local_path}")
        response = requests.get(url, stream=True, timeout=30, headers=headers)
        response.raise_for_status()

        # 保存文件
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"文件下载成功: {local_path}, 大小: {os.path.getsize(local_path)} bytes")

        # 验证文件是否为有效的媒体文件
        media_type = 'video' if any(ext in local_path.lower() for ext in ['.mp4', '.avi', '.mov', '.mkv']) else 'audio'
        if not validate_media_file(local_path, media_type):
            raise ValueError(f"下载的{media_type}文件无效或损坏")

        return local_path

    except Exception as e:
        # 清理可能损坏的文件
        if os.path.exists(local_path):
            try:
                os.remove(local_path)
                logger.info(f"清理损坏的文件: {local_path}")
            except:
                pass

        logger.error(f"下载文件失败: {url} -> {local_path}, 错误: {str(e)}")
        raise


def process_task(local_video_input, local_audio_input, output_path):
    result_path = inference(
        config=dinet_config,
        video_path=local_video_input,
        audio_path=local_audio_input,
        video_out_path=output_path,
        inference_ckpt_path=inference_ckpt_path,
        enable_cache=False  # 禁用缓存
    )
    return result_path


def run_once():
    # fetch task
    waiting_task_qs = DeferredGenerationTask.objects.filter(status=STATUS_WAITING)
    if not waiting_task_qs.exists():
        return

    with transaction.atomic():
        task = DeferredGenerationTask.objects.select_for_update(skip_locked=True) \
            .filter(status=STATUS_WAITING).first()
        if not task:
            return

        # 更新任务状态为处理中
        task.status = STATUS_INFERENCE
        task.save()

        logger.info(f"开始处理任务 {task.id}: {task.source_video}")

    # 在事务外处理任务，避免长时间锁定
    try:
        temp_dir = tempfile.mkdtemp(prefix=f'task_{task.id}_')
        logger.info(f"创建临时目录: {temp_dir}")

        # 处理视频文件 - 如果是URL则下载，否则直接使用本地路径
        if is_url(task.source_video):
            # 获取文件扩展名
            parsed_url = urlparse(task.source_video)
            path = parsed_url.path
            ext = os.path.splitext(path)[1] if path else ""
            if not ext:
                ext = ".mp4"  # 默认视频扩展名

            # 生成完整文件名
            filename = f"video_{uuid.uuid4().hex}{ext}"
            local_video_path = os.path.join(temp_dir, filename)
            download_file(task.source_video, local_video_path)
        else:
            local_video_path = task.source_video
            if not os.path.exists(local_video_path):
                raise FileNotFoundError(f"视频文件不存在: {local_video_path}")
            # 验证本地视频文件
            if not validate_media_file(local_video_path, 'video'):
                raise ValueError(f"本地视频文件无效: {local_video_path}")

        # 处理音频文件 - 如果是URL则下载，否则直接使用本地路径
        if is_url(task.source_audio):
            # 获取文件扩展名
            parsed_url = urlparse(task.source_audio)
            path = parsed_url.path
            ext = os.path.splitext(path)[1] if path else ""
            if not ext:
                ext = ".mp3"  # 默认音频扩展名

            # 生成完整文件名
            filename = f"audio_{uuid.uuid4().hex}{ext}"
            local_audio_path = os.path.join(temp_dir, filename)
            download_file(task.source_audio, local_audio_path)
        else:
            local_audio_path = task.source_audio
            if not os.path.exists(local_audio_path):
                raise FileNotFoundError(f"音频文件不存在: {local_audio_path}")
            # 验证本地音频文件
            if not validate_media_file(local_audio_path, 'audio'):
                raise ValueError(f"本地音频文件无效: {local_audio_path}")

        # 生成输出路径
        if task.generated_video_path:
            output_path = task.generated_video_path
        else:
            # 使用默认输出目录
            output_filename = f'output_{uuid.uuid4().hex}.mp4'
            output_path = os.path.join('generated_videos', output_filename)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:  # 只有当目录路径不为空时才创建目录
            os.makedirs(output_dir, exist_ok=True)

        logger.info(f"开始处理任务 {task.id}: video={local_video_path}, audio={local_audio_path}, output={output_path}")

        # 检查文件是否存在和大小
        logger.info(f"视频文件: {local_video_path}, 存在: {os.path.exists(local_video_path)}, 大小: {os.path.getsize(local_video_path) if os.path.exists(local_video_path) else 0}")
        logger.info(f"音频文件: {local_audio_path}, 存在: {os.path.exists(local_audio_path)}, 大小: {os.path.getsize(local_audio_path) if os.path.exists(local_audio_path) else 0}")

        # 处理任务
        result_path = process_task(local_video_path, local_audio_path, output_path)

        # upload output file here if using oss

        # 更新任务状态为完成
        with transaction.atomic():
            task.refresh_from_db()
            task.status = STATUS_FINISHED
            task.generated_video_path = result_path
            task.save()

        logger.info(f"任务 {task.id} 处理完成: {result_path}")

    except Exception as e:
        # 处理失败，更新状态为失败
        logger.error(f"任务 {task.id} 处理失败: {str(e)}", exc_info=True)

        with transaction.atomic():
            task.refresh_from_db()
            task.status = STATUS_FAILED
            task.save()

    finally:
        # 清理临时文件
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"清理临时目录: {temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时目录失败: {temp_dir}, 错误: {str(e)}")


def main():
    while True:
        try:
            connection.connect()
            run_once()
        finally:
            time.sleep(1)


if __name__ == '__main__':
    # run_once()
    main()
