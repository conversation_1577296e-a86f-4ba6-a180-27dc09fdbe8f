# -*- coding: utf-8 -*-

from rest_framework import serializers
from django.utils.module_loading import import_string
from .exceptions import ParameterError
from utils_common.variables import VAR
from pymodm_init.serializers import MongoModelSerializer

from .serializers import SimpleSerializer, many_serializer_getter_wrapper, safe_getter


class MongoExtraRelationSerializers(MongoModelSerializer):
    def __init__(self, *args, **kwargs):
        self.extras = kwargs.pop('extras', {})
        super(MongoExtraRelationSerializers, self).__init__(*args, **kwargs)

    def to_representation(self, instance):
        ret = MongoModelSerializer.to_representation(self, instance)
        for extra in self.extras:
            try:
                processor = getattr(self.Meta, 'extra__' + extra)
            except AttributeError:
                raise ParameterError("Can't get extra %s processor for serializer %s" % (extra, self.__class__))
            if isinstance(processor, VAR):
                serializer_class, getter, many = processor.serializer, processor.getter, processor.many
                if isinstance(serializer_class, str):
                    serializer_class = import_string(serializer_class)
                elif serializer_class is None:
                    serializer_class = SimpleSerializer
                if getter is None:
                    getter = safe_getter
                if many:
                    data = [serializer_class(instance=obj, context=self._context, extras=self.extras.get(extra, {})).data
                            if obj is not None else {} for obj in getter(instance, extra).all()]
                else:
                    obj = getter(instance, extra)
                    if obj is None:
                        data = {}
                    else:
                        data = serializer_class(instance=obj, context=self._context, extras=self.extras.get(extra, {})).data
            elif isinstance(processor, str):
                processor_class = import_string(processor)
                data = processor_class(instance, context=self._context, extras=self.extras.get(extra, {}), extra_name=extra).data
            else:
                data = processor(instance, context=self._context, extras=self.extras.get(extra, {}), extra_name=extra).data
            ret[extra] = data
        return ret
