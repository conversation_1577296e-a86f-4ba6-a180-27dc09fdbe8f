import os
import io
import json
import base64
from PIL import Image

from aliyunsdkcore import client
from aliyunsdksts.request.v20150401 import AssumeRoleRequest

from django.conf import settings
from django.shortcuts import redirect
from rest_framework.response import Response
from common_rest.views import CustomizedAPIView, expose
from django.core.files.uploadedfile import InMemoryUploadedFile, TemporaryUploadedFile
from django.db.models import Q
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator

from . import models, serializers
from .utils import format_file_size, to_string


OSS_ACCESS_KEY_ID = settings.OSS_ACCESS_KEY_ID
OSS_ACCESS_KEY_SECRET = settings.OSS_ACCESS_KEY_SECRET
OSS_ARN = settings.OSS_ARN
OSS_ENDPOINT = settings.OSS_ENDPOINT
OSS_REGION = settings.OSS_REGION
OSS_BUCKET_NAME = settings.OSS_BUCKET_NAME


class FileStorageView(CustomizedAPIView):
    model = models.OSSFileStorage
    serializer_class = serializers.OSSFileStorageSerializer
    lookup_field = 'id'

    def get(self, request):
        obj = self.get_object()
        return redirect(obj.url)

    def post(self, request):
        upload_data = request.data.get('data')
        size_in_bytes = request.data.get('size')
        upload_path = request.data.get('path')

        # check file type
        if upload_data and upload_path:
            bin_data = base64.b64decode(upload_data.split(',')[-1])
            # 需要检查
            file_obj = self.model.objects.create(
                path=upload_path,
                data=bin_data,
                size=size_in_bytes
            )
            data = {
                'id': file_obj.id,
                'path': file_obj.path,
                'oss_path': file_obj.oss_path,
                'size': file_obj.size,
                'url': file_obj.url,
            }
            return Response({'error_code': 0, 'error_reason': 'ok', 'data': data})
        else:
            return Response({'error_code': 1, 'error_reason': '上传文件或目标目录不能为空'}, 200)

    @expose(['POST'])
    def update_load(self, request):
        # 表单形式文件上传
        upload_file = request.data.get('upload_file', None)
        upload_path = request.data.get('path', '')
        _, file_extension = os.path.splitext(upload_path)
        if not upload_file:
            return Response({'error_code': 1, 'error_reason': '未检测到有效的文件'}, 200)
        filename = os.path.splitext(upload_file._name)[0]
        size_format = format_file_size(upload_file.size)

        upload_file.seek(0)  # 初始化文件句柄位置
        suffix = file_extension[1:].upper()
        file_obj = self.model.objects.create(
            path=upload_path,
            data=upload_file,
            size=upload_file.size
        )
        data = {
            'id': file_obj.id,
            'name': filename,
            'size': file_obj.size,
            'size_format': size_format,
            'suffix': suffix,
            'path': file_obj.path,
            'oss_path': file_obj.oss_path,
            'url': file_obj.url,
        }
        return Response({'error_code': 0, 'error_reason': 'ok', 'data': data})


    @expose(['POST'])
    def m_update_load(self, request):
        # 移动端表单形式文件上传
        upload_file = request.data.get('upload_file', None)
        upload_path = request.data.get('path', '')
        _, file_extension = os.path.splitext(upload_path)
        if not upload_file:
            return Response({'error_code': 1, 'error_reason': '未检测到有效的文件'}, 200)
        filename = os.path.splitext(upload_file._name)[0]
        size_format = format_file_size(upload_file.size)

        upload_file.seek(0)  # 初始化文件句柄位置
        suffix = file_extension[1:].upper()
        file_obj = self.model.objects.create(
            path=upload_path,
            data=upload_file,
            size=upload_file.size
        )
        data = {
            'id': file_obj.id,
            'name': filename,
            'size': file_obj.size,
            'size_format': size_format,
            'suffix': suffix,
            'path': file_obj.path,
            'oss_path': file_obj.oss_path,
            'url': file_obj.url,
        }
        return Response({'error_code': 0, 'error_reason': 'ok', 'data': data})

    @expose(['GET'])
    def get_sts_token(self, request):
        expire_seconds = 1800
        access_key_id = OSS_ACCESS_KEY_ID
        access_key_secret = OSS_ACCESS_KEY_SECRET
        bucket = OSS_BUCKET_NAME
        role_arn = OSS_ARN
        region_id = OSS_REGION
        policy_text = '{"Version": "1", "Statement": [{"Action": ["oss:GetObject", "oss:PutObject"], "Effect": "Allow", "Resource": ["acs:oss:*:*:%s/*"]}]}' % bucket

        clt = client.AcsClient(access_key_id, access_key_secret, region_id)
        req = AssumeRoleRequest.AssumeRoleRequest()

        req.set_DurationSeconds(expire_seconds)
        req.set_accept_format('json')
        req.set_RoleArn(role_arn)
        req.set_RoleSessionName('session-name')
        req.set_Policy(policy_text)
        body = clt.do_action_with_exception(req)

        token = json.loads(to_string(body))
        data = {
            "access_key_id": token['Credentials']['AccessKeyId'],
            "access_key_secret": token['Credentials']['AccessKeySecret'],
            "security_token": token['Credentials']['SecurityToken'],
        }
        return Response({'error_code': 0, 'error_reason': 'ok', 'data': data})

    @expose(['POST'])
    def delete_path(self, request):
        path = request.data.get('path', '')
        file_obj = self.model.objects.get(path=path)
        file_obj.delete()
        return Response({'error_code': 0, 'error_reason': 'ok'})


@method_decorator(login_required, name='dispatch')
class MediaCenterView(CustomizedAPIView):
    """资源中心视图"""
    model = models.OSSFileStorage

    def get(self, request):
        """获取用户的媒体资源列表"""
        media_type = request.GET.get('media_type', '')
        search = request.GET.get('search', '')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 基础查询 - 只显示当前用户的文件
        queryset = self.model.objects.filter(user_id=request.user.id)

        # 按素材类型过滤
        if media_type:
            queryset = queryset.filter(media_type=media_type)

        # 搜索过滤
        if search:
            queryset = queryset.filter(
                Q(original_name__icontains=search) |
                Q(description__icontains=search) |
                Q(path__icontains=search)
            )

        # 分页
        total = queryset.count()
        start = (page - 1) * page_size
        end = start + page_size
        files = queryset[start:end]

        data = []
        for file_obj in files:
            data.append({
                'id': file_obj.id,
                'original_name': file_obj.original_name,
                'path': file_obj.path,
                'media_type': file_obj.media_type,
                'media_type_display': file_obj.get_media_type_display(),
                'size': file_obj.size,
                'size_format': format_file_size(file_obj.size),
                'description': file_obj.description,
                'url': file_obj.url,
                'created_at': file_obj.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            })

        return Response({
            'error_code': 0,
            'error_reason': 'ok',
            'data': {
                'files': data,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        })

    def post(self, request):
        """上传媒体文件到资源中心"""
        upload_file = request.data.get('upload_file', None)
        media_type = request.data.get('media_type', '')
        description = request.data.get('description', '')

        if not upload_file:
            return Response({'error_code': 1, 'error_reason': '未检测到有效的文件'}, 200)

        if not media_type:
            return Response({'error_code': 1, 'error_reason': '请选择素材类型'}, 200)

        # 验证文件类型
        filename = upload_file.name
        file_extension = os.path.splitext(filename)[1].lower()

        if media_type == models.MEDIA_TYPE_VIDEO:
            allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
        else:  # audio
            allowed_extensions = ['.mp3', '.wav', '.aac', '.flac', '.ogg']

        if file_extension not in allowed_extensions:
            return Response({
                'error_code': 1,
                'error_reason': f'不支持的文件格式，支持的格式: {", ".join(allowed_extensions)}'
            }, 200)

        # 生成存储路径
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"
        storage_path = f"media_center/{request.user.id}/{media_type}/{unique_filename}"

        upload_file.seek(0)  # 重置文件指针

        # 创建文件记录
        file_obj = self.model.objects.create(
            path=storage_path,
            data=upload_file,
            size=upload_file.size,
            user_id=request.user.id,
            username=request.user.username,
            media_type=media_type,
            original_name=filename,
            description=description
        )

        return Response({
            'error_code': 0,
            'error_reason': 'ok',
            'data': {
                'id': file_obj.id,
                'original_name': file_obj.original_name,
                'media_type': file_obj.media_type,
                'media_type_display': file_obj.get_media_type_display(),
                'size': file_obj.size,
                'size_format': format_file_size(file_obj.size),
                'url': file_obj.url,
                'created_at': file_obj.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            }
        })

    @expose(['DELETE'])
    def delete_media(self, request):
        """删除媒体文件"""
        file_id = request.data.get('id')

        if not file_id:
            return Response({'error_code': 1, 'error_reason': '缺少文件ID'}, 200)

        try:
            file_obj = self.model.objects.get(id=file_id, user_id=request.user.id)
            file_obj.delete()

            return Response({'error_code': 0, 'error_reason': 'ok'})
        except self.model.DoesNotExist:
            return Response({'error_code': 1, 'error_reason': '文件不存在或无权限删除'}, 200)


