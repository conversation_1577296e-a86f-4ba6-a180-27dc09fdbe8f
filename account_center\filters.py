import django_filters
from django_filters.rest_framework.filterset import FilterSet
from django.db.models import Q

from . import models


def name_keyword_search(queryset, name, value):
    queryset = queryset.filter(Q(name__icontains=value) | Q(user__username__icontains=value))
    return queryset



class AccountFilter(FilterSet):
    o = django_filters.OrderingFilter(
        fields=('pk', '-pk', 'create_time', '-create_time', 'update_time', '-update_time'))
    name = django_filters.CharFilter(label='name or username', method=name_keyword_search)

    class Meta:
        model = models.Account
        fields = {
            'name': ['exact', 'icontains'],
            'phone_number': ['exact', 'icontains'],
        }
