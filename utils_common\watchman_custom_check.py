from watchman.decorators import check
from pymongo import MongoClient
from django.db import connections
from django.conf import settings


@check
def _check_mongo(name, host, port):
    target = 'mongodb://%s:%s/' % (host, port)
    client = MongoClient(target)
    client.server_info()
    return {name: {'ok': True}}


def _check_mongos():
    mongo_dbs = getattr(settings, 'MONGO_DBS', None)
    if mongo_dbs is None:
        return 'no MONGO_DBS in settings'
    return [_check_mongo(_['DBNAME'], _['HOST'], _.get('PORT', 27017)) for _ in mongo_dbs.values()]


def check_mongo():
    return {'mongodb': _check_mongos()}


@check
def _check_database(database):
    connections[database].cursor()
    return {database: {"ok": True}}


def _check_databases(databases):
    return [_check_database(database) for database in sorted(databases)]


def databases():
    return {"databases": _check_databases(settings.DATABASES)}
