#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for DINet inference with caching functionality
"""

import os
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from form_api.tools import inference, clear_inference_cache, get_inference_cache_stats


class SimpleConfig:
    """Simple configuration class to replace OmegaConf"""
    def __init__(self):
        self.model = SimpleNamespace()
        self.data = SimpleNamespace()


class SimpleNamespace:
    """Simple namespace class"""
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)


def test_inference_with_cache():
    """Test DINet inference with caching enabled"""

    print("🧪 Testing DINet Inference with Caching")
    print("=" * 50)

    # Example configuration (adjust paths as needed)
    config_content = """
    model:
      cross_attention_dim: 768

    data:
      num_frames: 5
      resolution: 512
      num_face_ref: 1
      mask_image_path: "./checkpoints/mask/mouth_mask.png"
    """

    # Create simple config object
    config = SimpleConfig()

    # Mock config for testing
    config.model.cross_attention_dim = 768
    config.data.num_frames = 5
    config.data.resolution = 512
    config.data.num_face_ref = 1
    config.data.mask_image_path = "./checkpoints/mask/mouth_mask.png"

    # Test paths (these should be updated with actual file paths)
    test_video_path = "./test_data/test_video.mp4"
    test_audio_path = "./test_data/test_audio.wav"
    test_output_path = "./output/test_output_cached.mp4"
    checkpoint_path = "./checkpoints/dinet.pth"

    print(f"📹 Video: {test_video_path}")
    print(f"🎵 Audio: {test_audio_path}")
    print(f"📤 Output: {test_output_path}")
    print(f"📦 Checkpoint: {checkpoint_path}")
    print()

    # Test 1: Clear cache first
    print("🗑️  Step 1: Clearing existing cache...")
    clear_inference_cache()

    # Test 2: First run (no cache)
    print("⚡ Step 2: First inference run (no cache)...")
    start_time = time.time()

    try:
        result = inference(
            config=config,
            video_path=test_video_path,
            audio_path=test_audio_path,
            video_out_path=test_output_path,
            inference_ckpt_path=checkpoint_path,
            seed=1247,
            enable_cache=True
        )
        first_run_time = time.time() - start_time
        print(f"✅ First run completed in {first_run_time:.2f}s")
        print(f"📁 Output saved to: {result}")

    except Exception as e:
        print(f"❌ First run failed: {e}")
        print("ℹ️  This might be due to missing test files or checkpoints")
        return False

    # Test 3: Show cache stats
    print("\n📊 Cache statistics after first run:")
    stats = get_inference_cache_stats()
    if stats:
        for key, value in stats.items():
            print(f"   {key}: {value}")

    # Test 4: Second run (with cache)
    print("\n🔥 Step 3: Second inference run (with cache)...")
    test_output_path_2 = "./output/test_output_cached_2.mp4"
    start_time = time.time()

    try:
        result = inference(
            config=config,
            video_path=test_video_path,
            audio_path=test_audio_path,
            video_out_path=test_output_path_2,
            inference_ckpt_path=checkpoint_path,
            seed=1247,
            enable_cache=True
        )
        second_run_time = time.time() - start_time
        print(f"✅ Second run completed in {second_run_time:.2f}s")
        print(f"📁 Output saved to: {result}")

        # Compare performance
        speedup = first_run_time / second_run_time if second_run_time > 0 else 0
        print(f"🚀 Performance improvement: {speedup:.2f}x faster")

    except Exception as e:
        print(f"❌ Second run failed: {e}")
        return False

    # Test 5: Final cache stats
    print("\n📊 Final cache statistics:")
    stats = get_inference_cache_stats()
    if stats:
        for key, value in stats.items():
            print(f"   {key}: {value}")

    print("\n✅ Cache testing completed successfully!")
    return True


def test_cache_utilities():
    """Test cache utility functions"""

    print("\n🔧 Testing Cache Utilities")
    print("=" * 30)

    # Test cache stats
    print("📊 Getting cache statistics...")
    stats = get_inference_cache_stats()
    if stats:
        print("Cache stats retrieved:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
    else:
        print("No cache stats available")

    # Test cache clearing
    print("\n🗑️  Testing cache clearing...")
    result = clear_inference_cache()
    print(f"Cache clear result: {'✅ Success' if result else '❌ Failed'}")


if __name__ == "__main__":
    print("🚀 DINet Cache Testing Suite")
    print("=" * 40)

    # Create necessary directories
    os.makedirs("./output", exist_ok=True)
    os.makedirs("./test_data", exist_ok=True)

    # Test cache utilities first
    test_cache_utilities()

    # Test inference with cache (only if files exist)
    print(f"\n{'='*40}")
    print("ℹ️  To test full inference with cache:")
    print("   1. Place test video at: ./test_data/test_video.mp4")
    print("   2. Place test audio at: ./test_data/test_audio.wav")
    print("   3. Ensure checkpoint exists at: ./checkpoints/dinet.pth")
    print("   4. Ensure mask exists at: ./checkpoints/mask/mouth_mask.png")
    print("   5. Run this script again")

    # Check if test files exist
    required_files = [
        "./test_data/test_video.mp4",
        "./test_data/test_audio.wav",
        "./checkpoints/dinet.pth"
    ]

    files_exist = all(os.path.exists(f) for f in required_files)

    if files_exist:
        print("\n✅ Test files found! Running full inference test...")
        test_inference_with_cache()
    else:
        print(f"\n⚠️  Missing test files:")
        for f in required_files:
            status = "✅" if os.path.exists(f) else "❌"
            print(f"   {status} {f}")

    print("\n🎉 Testing complete!")
