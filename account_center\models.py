from django.db import models
from django.contrib.auth.models import User


class Account(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=20, null=True, blank=True, verbose_name="账户名称")
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name="电话号码")

    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = '账户'
        verbose_name_plural = '账户'

    @property
    def username(self):
        return self.user.username

    def __str__(self):
        return self.name

