#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick validation script for DINet caching functionality
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_cache_imports():
    """Test if cache imports work correctly"""
    print("🧪 Testing cache imports...")

    try:
        from form_api.tools import get_inference_cache_stats, clear_inference_cache
        print("✅ Cache functions imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Cache import failed: {e}")
        return False

def test_cache_initialization():
    """Test cache manager initialization"""
    print("\n🧪 Testing cache initialization...")

    try:
        # Try full cache system first
        sys.path.append(str(project_root))

        try:
            from dinet_cache_strategy import DINetCacheManager
            cache_manager = DINetCacheManager(enable_redis=False)  # Disable Redis for testing
            print("✅ Full cache system initialized")
            cache_type = "full"
        except ImportError:
            from simple_cache import DINetCacheManager
            cache_manager = DINetCacheManager()
            print("✅ Simple cache system initialized")
            cache_type = "simple"

        # Test basic operations
        stats = cache_manager.get_cache_stats()
        print(f"📊 Cache stats: {stats}")

        return True, cache_type

    except Exception as e:
        print(f"❌ Cache initialization failed: {e}")
        return False, None

def test_cache_operations():
    """Test basic cache operations"""
    print("\n🧪 Testing cache operations...")

    try:
        from form_api.tools import get_inference_cache_stats, clear_inference_cache

        # Test stats
        stats = get_inference_cache_stats()
        if stats is not None:
            print(f"✅ Cache stats retrieved: {len(stats)} metrics")
        else:
            print("⚠️  Cache stats returned None (cache might not be available)")

        # Test clear (but don't actually clear in production)
        print("📝 Cache clear function available (not executed)")

        return True

    except Exception as e:
        print(f"❌ Cache operations failed: {e}")
        return False

def main():
    """Main validation function"""
    print("🚀 DINet Cache System Validation")
    print("=" * 35)

    results = []

    # Test 1: Imports
    results.append(test_cache_imports())

    # Test 2: Initialization
    init_result, cache_type = test_cache_initialization()
    results.append(init_result)

    if init_result:
        print(f"📦 Cache type: {cache_type}")

    # Test 3: Operations
    results.append(test_cache_operations())

    # Summary
    print(f"\n📋 Test Summary")
    print("-" * 15)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")

    if passed == total:
        print("✅ All tests passed! Cache system is ready to use.")
        print("\n🎯 Next steps:")
        print("1. Use enable_cache=True in inference() calls")
        print("2. Monitor cache performance with get_inference_cache_stats()")
        print("3. Set up periodic cache cleanup if needed")
    else:
        print("❌ Some tests failed. Check error messages above.")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all required files are present")
        print("2. Check Python path configuration")
        print("3. Verify cache directory permissions")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
