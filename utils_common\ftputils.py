import socket
import ftplib


class FTPWrapper(ftplib.FTP):

    def stor_binary_to_remote(self, fd, remote_filename, blocksize=8192, callback=None, rest=None):
        return self.storbinary("STOR %s" % remote_filename, fd, blocksize, callback, rest)

    def stor_file_to_remote(self, filename, remote_filename, blocksize=8192, callback=None, rest=None):
        with open(filename, 'rb') as fd:
            return self.stor_binary_to_remote(fd, remote_filename, blocksize, callback, rest)

    def copy_binary_from_remote(self, fd, remote_filename):
        def _call_back(buf):
            fd.write(buf)

        return self.retrbinary('RETR %s' % remote_filename, _call_back)

    def copy_file_from_remote(self, filename, remote_filename):
        with open(filename, 'wb') as fd:
            return self.copy_binary_from_remote(fd, remote_filename)

    def cd_and_make_remote_dir(self, remote_dirname):
        if remote_dirname.endswith('/'):
            remote_dirname = remote_dirname[:-1]
        if remote_dirname.startswith('/'):
            self.cwd('/')
            remote_dirs = remote_dirname[1:].split("/")
        else:
            remote_dirs = remote_dirname.split("/")
        for dirname in remote_dirs:
            try:
                self.cwd(dirname)
            except:
                self.mkd(dirname)
                self.cwd(dirname)
        if self.pwd() != remote_dirname:
            raise ftplib.Error("some error may occur when cd and make remote dir %s." % remote_dirname)

    def __enter__(self):
        """Context management protocol.  Returns self."""
        return self

    def __exit__(self, *args):
        """Context management protocol.

        Will try to quit() if active, then close().
        If not active, a simple close() call is made.
        """
        if self.sock is not None and self.getwelcome() is not None:
            try:
                self.quit()
            except socket.error as e:
                self.close()
                # [Errno 61] is Connection refused
                # if the error is different, we want to raise it.
                if e.errno != 61:
                    raise
            except Exception as e:
                self.close()
        else:
            self.close()
