{% extends 'base.html' %}

{% block title %}任务管理 - DINet 口型同步系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tasks me-2"></i>任务管理</h2>
    <div>
        <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#createTaskModal">
            <i class="fas fa-plus me-2"></i>创建任务
        </button>
        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#batchCreateModal">
            <i class="fas fa-layer-group me-2"></i>批量创建
        </button>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">状态筛选</label>
                <select name="status" class="form-select">
                    <option value="">全部状态</option>
                    <option value="waiting" {% if request.GET.status == 'waiting' %}selected{% endif %}>等待中</option>
                    <option value="inference" {% if request.GET.status == 'inference' %}selected{% endif %}>处理中</option>
                    <option value="finished" {% if request.GET.status == 'finished' %}selected{% endif %}>已完成</option>
                    <option value="failed" {% if request.GET.status == 'failed' %}selected{% endif %}>失败</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">用户名搜索</label>
                <input type="text" name="username" class="form-control" value="{{ request.GET.username }}" placeholder="输入用户名">
            </div>
            <div class="col-md-3">
                <label class="form-label">排序方式</label>
                <select name="o" class="form-select">
                    <option value="-created_at" {% if request.GET.o == '-created_at' %}selected{% endif %}>创建时间(新到旧)</option>
                    <option value="created_at" {% if request.GET.o == 'created_at' %}selected{% endif %}>创建时间(旧到新)</option>
                    <option value="-updated_at" {% if request.GET.o == '-updated_at' %}selected{% endif %}>更新时间(新到旧)</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <a href="{% url 'task_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>重置
                </a>
            </div>
        </form>
    </div>
</div>

<!-- 任务统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.waiting }}</h4>
                        <p class="mb-0">等待中</p>
                    </div>
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.inference }}</h4>
                        <p class="mb-0">处理中</p>
                    </div>
                    <i class="fas fa-cog fa-spin fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.finished }}</h4>
                        <p class="mb-0">已完成</p>
                    </div>
                    <i class="fas fa-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.failed }}</h4>
                        <p class="mb-0">失败</p>
                    </div>
                    <i class="fas fa-times fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="row" id="taskList">
    {% for task in tasks %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card task-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span class="badge bg-secondary">ID: {{ task.id }}</span>
                <span class="badge {% if task.status == 'waiting' %}bg-warning{% elif task.status == 'inference' %}bg-info{% elif task.status == 'finished' %}bg-success{% else %}bg-danger{% endif %}">
                    {{ task.get_status_display }}
                </span>
            </div>
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-user me-1"></i>{{ task.username|default:"未知用户" }}
                </h6>

                <div class="mb-2">
                    <small class="text-muted">源视频:</small>
                    <div class="text-truncate" title="{{ task.source_video }}">
                        {{ task.source_video|truncatechars:30 }}
                    </div>
                </div>

                <div class="mb-2">
                    <small class="text-muted">源音频:</small>
                    <div class="text-truncate" title="{{ task.source_audio }}">
                        {{ task.source_audio|truncatechars:30 }}
                    </div>
                </div>

                {% if task.generated_video_path %}
                <div class="mb-2">
                    <small class="text-muted">生成视频:</small>
                    <div class="text-truncate" title="{{ task.generated_video_path }}">
                        {{ task.generated_video_path|truncatechars:30 }}
                    </div>
                </div>
                {% endif %}

                <div class="row text-center mt-3">
                    <div class="col-6">
                        <small class="text-muted">创建时间</small>
                        <div class="small">{{ task.created_at|date:"m-d H:i" }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">更新时间</small>
                        <div class="small">{{ task.updated_at|date:"m-d H:i" }}</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTask({{ task.id }})">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                    {% if task.status == 'finished' and task.generated_video_path %}
                    <button class="btn btn-sm btn-outline-success" onclick="downloadResult({{ task.id }})">
                        <i class="fas fa-download"></i> 下载
                    </button>
                    {% endif %}
                    {% if task.status == 'failed' %}
                    <button class="btn btn-sm btn-outline-warning" onclick="retryTask({{ task.id }})">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTask({{ task.id }})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无任务</h5>
            <p class="text-muted">点击"创建任务"开始您的第一个任务</p>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 分页 -->
{% if is_paginated %}
<nav aria-label="任务分页">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.username %}&username={{ request.GET.username }}{% endif %}{% if request.GET.o %}&o={{ request.GET.o }}{% endif %}">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.username %}&username={{ request.GET.username }}{% endif %}{% if request.GET.o %}&o={{ request.GET.o }}{% endif %}">上一页</a>
            </li>
        {% endif %}

        <li class="page-item active">
            <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
        </li>

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.username %}&username={{ request.GET.username }}{% endif %}{% if request.GET.o %}&o={{ request.GET.o }}{% endif %}">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.username %}&username={{ request.GET.username }}{% endif %}{% if request.GET.o %}&o={{ request.GET.o }}{% endif %}">末页</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 创建任务模态框 -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>创建新任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 选择输入方式 -->
                <ul class="nav nav-tabs mb-3" id="inputMethodTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual" type="button">
                            <i class="fas fa-keyboard me-1"></i>手动输入
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="media-center-tab" data-bs-toggle="tab" data-bs-target="#media-center" type="button">
                            <i class="fas fa-folder-open me-1"></i>从资源中心选择
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="inputMethodContent">
                    <!-- 手动输入 -->
                    <div class="tab-pane fade show active" id="manual" role="tabpanel">
                        <form id="createTaskForm">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">源视频 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="source_video" required placeholder="输入视频文件路径或URL">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">源音频 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="source_audio" required placeholder="输入音频文件路径或URL">
                            </div>
                        </form>
                    </div>

                    <!-- 从资源中心选择 -->
                    <div class="tab-pane fade" id="media-center" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>选择视频</h6>
                                <div class="border rounded p-3 mb-3" style="height: 200px; overflow-y: auto;">
                                    <div id="videoList">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                                        </div>
                                    </div>
                                </div>
                                <div id="selectedVideo" class="alert alert-info" style="display: none;">
                                    <strong>已选择:</strong> <span id="selectedVideoName"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>选择音频</h6>
                                <div class="border rounded p-3 mb-3" style="height: 200px; overflow-y: auto;">
                                    <div id="audioList">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                                        </div>
                                    </div>
                                </div>
                                <div id="selectedAudio" class="alert alert-success" style="display: none;">
                                    <strong>已选择:</strong> <span id="selectedAudioName"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTask()">
                    <i class="fas fa-plus me-1"></i>创建任务
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量创建模态框 -->
<div class="modal fade" id="batchCreateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-layer-group me-2"></i>批量创建任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>方式一：JSON 格式</h6>
                        <textarea id="jsonInput" class="form-control" rows="10" placeholder='[
  {
    "source_video": "/path/to/video1.mp4",
    "source_audio": "/path/to/audio1.wav"
  },
  {
    "source_video": "/path/to/video2.mp4",
    "source_audio": "/path/to/audio2.wav"
  }
]'></textarea>
                    </div>
                    <div class="col-md-6">
                        <h6>方式二：文件上传</h6>
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p>拖拽 JSON 文件到此处或点击选择文件</p>
                            <input type="file" id="fileInput" accept=".json" style="display: none;">
                            <button type="button" class="btn btn-outline-primary" onclick="$('#fileInput').click()">
                                选择文件
                            </button>
                        </div>
                        <div id="fileInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file-alt me-2"></i>
                                <span id="fileName"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="batchCreateTasks()">
                    <i class="fas fa-layer-group me-1"></i>批量创建
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let autoRefresh = true;
let refreshInterval;
let selectedVideoId = null;
let selectedAudioId = null;

$(document).ready(function() {
    startAutoRefresh();
    setupFileUpload();
});

function startAutoRefresh() {
    if (autoRefresh) {
        refreshInterval = setInterval(function() {
            location.reload();
        }, 30000); // 30秒刷新一次
    }
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

// 获取CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

const csrftoken = getCookie('csrftoken');

// 设置AJAX默认配置
$.ajaxSetup({
    beforeSend: function(xhr, settings) {
        if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
            // Only send the token to relative URLs i.e. locally.
            xhr.setRequestHeader("X-CSRFToken", csrftoken);
        }
    }
});

// 创建单个任务
function createTask() {
    let taskData = {};

    // 检查当前是哪个标签页
    if ($('#manual-tab').hasClass('active')) {
        // 手动输入模式
        const form = $('#createTaskForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        taskData = {
            source_video: $('input[name="source_video"]').val(),
            source_audio: $('input[name="source_audio"]').val()
        };
    } else {
        // 资源中心选择模式
        if (!selectedVideoId || !selectedAudioId) {
            showAlert('warning', '请选择视频和音频文件');
            return;
        }

        // 获取选中文件的URL
        taskData = {
            source_video_id: selectedVideoId,
            source_audio_id: selectedAudioId
        };
    }

    $.ajax({
        url: '/task/',
        type: 'POST',
        data: JSON.stringify(taskData),
        contentType: 'application/json',
        success: function(response) {
            if (response.error_code === 0) {
                showAlert('success', '任务创建成功！');
                $('#createTaskModal').modal('hide');
                // 重置表单和选择
                $('#createTaskForm')[0].reset();
                selectedVideoId = null;
                selectedAudioId = null;
                $('#selectedVideo, #selectedAudio').hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', '创建失败: ' + response.error_reason);
            }
        },
        error: function(xhr) {
            console.error('Error:', xhr.responseText);
            showAlert('danger', '请求失败: ' + (xhr.responseJSON?.error_reason || xhr.responseText));
        }
    });
}

// 批量创建任务
function batchCreateTasks() {
    let data;

    // 优先使用文件内容
    const fileContent = $('#fileInput')[0].files[0];
    if (fileContent) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                data = JSON.parse(e.target.result);
                submitBatchTasks(data);
            } catch (error) {
                showAlert('danger', 'JSON 文件格式错误: ' + error.message);
            }
        };
        reader.readAsText(fileContent);
    } else {
        // 使用文本框内容
        const jsonText = $('#jsonInput').val().trim();
        if (!jsonText) {
            showAlert('warning', '请输入 JSON 数据或上传文件');
            return;
        }

        try {
            data = JSON.parse(jsonText);
            submitBatchTasks(data);
        } catch (error) {
            showAlert('danger', 'JSON 格式错误: ' + error.message);
        }
    }
}

function submitBatchTasks(data) {
    $.ajax({
        url: '/task/batch_create/',  // 修复 URL
        type: 'POST',
        data: JSON.stringify({data: data}),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(response) {
            if (response.error_code === 0) {
                $('#batchCreateModal').modal('hide');
                showAlert('success', `批量创建成功！共创建 ${response.data.created} 个任务`);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', '批量创建失败: ' + response.error_reason);
            }
        },
        error: function(xhr) {
            showAlert('danger', '请求失败: ' + xhr.responseText);
        }
    });
}

// 查看任务详情
function viewTask(taskId) {
    $.ajax({
        url: '/task/web/task/' + taskId + '/',  // 修复 URL
        type: 'GET',
        success: function(response) {
            if (response.error_code === 0) {
                const task = response.data;
                const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td>任务ID:</td><td>${task.id}</td></tr>
                                <tr><td>用户:</td><td>${task.username || '未知'}</td></tr>
                                <tr><td>状态:</td><td><span class="badge bg-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td></tr>
                                <tr><td>创建时间:</td><td>${new Date(task.created_at).toLocaleString()}</td></tr>
                                <tr><td>更新时间:</td><td>${new Date(task.updated_at).toLocaleString()}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>文件信息</h6>
                            <table class="table table-sm">
                                <tr><td>源视频:</td><td class="text-break">${task.source_video}</td></tr>
                                <tr><td>源音频:</td><td class="text-break">${task.source_audio}</td></tr>
                                <tr><td>生成视频:</td><td class="text-break">${task.generated_video_path || '未生成'}</td></tr>
                            </table>
                        </div>
                    </div>
                `;
                $('#taskDetailContent').html(content);
                $('#taskDetailModal').modal('show');
            }
        }
    });
}

// 删除任务
function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
        $.ajax({
            url: '/task/',
            type: 'DELETE',
            data: JSON.stringify({id: taskId}),
            contentType: 'application/json',
            success: function(response) {
                if (response.error_code === 0) {
                    showAlert('success', '任务删除成功！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', '删除失败: ' + response.error_reason);
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr.responseText);
                showAlert('danger', '请求失败: ' + (xhr.responseJSON?.error_reason || xhr.responseText));
            }
        });
    }
}

// 下载结果
function downloadResult(taskId) {
    window.open(`/task/web/download/${taskId}/`, '_blank');
}

// 文件上传设置
function setupFileUpload() {
    const uploadArea = $('#uploadArea');
    const fileInput = $('#fileInput');

    // 拖拽上传
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            showFileInfo(files[0]);
        }
    });

    // 文件选择
    fileInput.on('change', function() {
        if (this.files.length > 0) {
            showFileInfo(this.files[0]);
        }
    });
}

function showFileInfo(file) {
    $('#fileName').text(file.name);
    $('#fileInfo').show();
}

// 工具函数
function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('main .container').prepend(alert);
}

function getStatusColor(status) {
    const colors = {
        'waiting': 'warning',
        'inference': 'info',
        'finished': 'success',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'waiting': '等待中',
        'inference': '处理中',
        'finished': '已完成',
        'failed': '失败'
    };
    return texts[status] || '未知';
}

// 重试任务
function retryTask(taskId) {
    if (confirm('确定要重试这个任务吗？')) {
        $.ajax({
            url: '/task/retry/',
            type: 'POST',
            data: JSON.stringify({id: taskId}),
            contentType: 'application/json',
            success: function(response) {
                if (response.error_code === 0) {
                    showAlert('success', '任务已重新加入队列！');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', '重试失败: ' + response.error_reason);
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr.responseText);
                showAlert('danger', '请求失败: ' + (xhr.responseJSON?.error_reason || xhr.responseText));
            }
        });
    }
}

// 当切换到资源中心选择时，加载媒体文件
$('#media-center-tab').on('shown.bs.tab', function() {
    loadMediaFiles();
});

// 加载媒体文件
function loadMediaFiles() {
    // 加载视频
    $.ajax({
        url: '/file/media-center/',
        type: 'GET',
        data: { media_type: 'video', page_size: 100 },
        success: function(response) {
            if (response.error_code === 0) {
                renderMediaList(response.data.files, 'video');
            }
        }
    });

    // 加载音频
    $.ajax({
        url: '/file/media-center/',
        type: 'GET',
        data: { media_type: 'audio', page_size: 100 },
        success: function(response) {
            if (response.error_code === 0) {
                renderMediaList(response.data.files, 'audio');
            }
        }
    });
}

// 渲染媒体列表
function renderMediaList(files, mediaType) {
    const container = mediaType === 'video' ? $('#videoList') : $('#audioList');
    container.empty();

    if (files.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-folder-open fa-2x mb-2"></i><br>
                暂无${mediaType === 'video' ? '视频' : '音频'}文件<br>
                <a href="/file/media-center/" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                    去上传
                </a>
            </div>
        `);
        return;
    }

    files.forEach(file => {
        const icon = mediaType === 'video' ? 'fas fa-video text-primary' : 'fas fa-music text-success';
        container.append(`
            <div class="media-item p-2 border rounded mb-2" style="cursor: pointer;"
                 onclick="selectMedia(${file.id}, '${file.original_name}', '${file.url}', '${mediaType}')">
                <div class="d-flex align-items-center">
                    <i class="${icon} me-2"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${file.original_name}</div>
                        <small class="text-muted">${file.size_format}</small>
                    </div>
                </div>
            </div>
        `);
    });
}

// 选择媒体文件
function selectMedia(fileId, fileName, fileUrl, mediaType) {
    if (mediaType === 'video') {
        selectedVideoId = fileId;
        $('#selectedVideoName').text(fileName);
        $('#selectedVideo').show();
        // 高亮选中的项
        $('#videoList .media-item').removeClass('border-primary bg-light');
        $(`#videoList .media-item:contains('${fileName}')`).addClass('border-primary bg-light');
    } else {
        selectedAudioId = fileId;
        $('#selectedAudioName').text(fileName);
        $('#selectedAudio').show();
        // 高亮选中的项
        $('#audioList .media-item').removeClass('border-success bg-light');
        $(`#audioList .media-item:contains('${fileName}')`).addClass('border-success bg-light');
    }
}
</script>
{% endblock %}




