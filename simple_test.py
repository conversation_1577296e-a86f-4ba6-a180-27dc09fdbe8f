#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Inference API Test Script
"""

import requests
import json

# Configuration - Please modify according to actual situation
API_URL = "http://localhost:8000/form/inference/"
VIDEO_URL = "https://your-domain.com/test_video.mp4"  # Please replace with actual video URL
AUDIO_URL = "https://your-domain.com/test_audio.mp3"  # Please replace with actual audio URL

def test_inference():
    """Test inference API"""

    # Request data
    data = {
        "video_url": VIDEO_URL,
        "audio_url": AUDIO_URL
    }

    # Request headers
    headers = {
        "Content-Type": "application/json"
    }

    print("Sending request...")
    print(f"URL: {API_URL}")
    print(f"Data: {json.dumps(data, indent=2)}")

    try:
        # Send request
        response = requests.post(API_URL, json=data, headers=headers, timeout=300)

        print(f"Status code: {response.status_code}")

        # Print response
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print("Response:")
            print(json.dumps(result, indent=2))

            # Check result
            if result.get('error_code') == 0:
                print("\n✅ Success!")
                video_url = result.get('data', {}).get('video_url')
                print(f"Generated video URL: {video_url}")
            else:
                print(f"\n❌ Failed: {result.get('error_reason')}")
        else:
            print("Response:", response.text)

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("Inference API Test")
    print("=" * 50)
    test_inference()